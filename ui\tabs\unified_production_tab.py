"""
Onglet unifié de production regroupant les unités de concassage et laverie
avec leurs consommations et arrêts.
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QMessageBox, QTabWidget,
                            QGroupBox, QLabel, QGridLayout, QFrame, QSplitter,
                            QScrollArea, QWidget, QPushButton, QComboBox, QSpinBox,
                            QDoubleSpinBox, QDateEdit, QTextEdit, QFormLayout)
from PyQt5.QtCore import Qt, QDate, QTimer, pyqtSignal
from PyQt5.QtGui import QFont
from datetime import datetime, timedelta, date
import logging

from .base_optimized import BaseOptimizedTab, QuickInputWidget, StatusWidget
from ui.widgets.professional_widgets import KPILine, StatusIndicator, ActionButton, ProfessionalTable
from models.enums import ProcessStepEnum, StockMovementType, ResourceType
from ui.professional_theme import SotramineTheme
from utils.date_utils import normalize_date, get_date_range_for_day, format_date_for_display

class UnifiedProductionTab(BaseOptimizedTab):
    """Onglet unifié pour la gestion de la production des deux unités."""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "🏭 Production Unifiée", parent)
        self.setup_interface()
        self.setup_timers()
        
    def setup_interface(self):
        """Configure l'interface unifiée simplifiée avec défilement."""
        # Zone unique avec défilement pour les contrôles de saisie
        main_scroll = QScrollArea()
        main_scroll.setWidgetResizable(True)
        main_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        main_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.main_layout.addWidget(main_scroll)

        # Widget contenu principal
        main_content = QWidget()
        main_layout = QVBoxLayout(main_content)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # Configurer la zone de défilement
        main_scroll.setWidget(main_content)

        # === CONFIGURATION DU PANNEAU PRINCIPAL ===
        self.setup_main_panel(main_layout)
        
        # Charger les données initiales
        self.update_kpi_title()
        self.check_existing_data()
        self.load_data_for_date()
        self.refresh_all_data()
        
    def setup_main_panel(self, layout):
        """Configure le panneau principal avec KPI et saisie."""
        # Section sélection de date
        self.setup_date_selection(layout)

        # Section KPI globale
        self.setup_global_kpi(layout)

        # Onglets pour les unités
        self.setup_unit_tabs(layout)

        # Section arrêts
        self.setup_downtime_section(layout)

        # Section lien vers historique
        self.setup_history_link(layout)

        # Espacement final pour éviter l'encombrement
        layout.addStretch()

    def setup_date_selection(self, layout):
        """Configure la section de sélection de date."""
        date_group = QGroupBox("📅 Date de Production")
        date_group.setFont(QFont("Segoe UI", 9, QFont.Bold))
        date_group.setMaximumHeight(120)  # Limiter la hauteur
        date_main_layout = QVBoxLayout(date_group)
        date_main_layout.setSpacing(5)

        # Première ligne : Sélecteur de date
        date_line1 = QHBoxLayout()
        self.production_date = QDateEdit()
        self.production_date.setCalendarPopup(True)
        self.production_date.setDate(QDate.currentDate())
        self.production_date.setDisplayFormat("dd/MM/yyyy")  # Format plus compact
        self.production_date.dateChanged.connect(self.on_date_changed)
        self.production_date.setMaximumWidth(120)

        date_line1.addWidget(QLabel("Date:"))
        date_line1.addWidget(self.production_date)
        date_line1.addStretch()

        # Indicateur de données existantes
        self.data_indicator = QLabel("📊 Données disponibles")
        self.data_indicator.setStyleSheet("color: green; font-weight: bold; font-size: 11px;")
        date_line1.addWidget(self.data_indicator)

        # Deuxième ligne : Boutons de navigation
        date_line2 = QHBoxLayout()

        prev_day_btn = QPushButton("◀ Précédent")
        prev_day_btn.setMaximumWidth(80)
        prev_day_btn.clicked.connect(self.go_to_previous_day)

        today_btn = QPushButton("Aujourd'hui")
        today_btn.setMaximumWidth(80)
        today_btn.clicked.connect(self.go_to_today)

        next_day_btn = QPushButton("Suivant ▶")
        next_day_btn.setMaximumWidth(80)
        next_day_btn.clicked.connect(self.go_to_next_day)

        date_line2.addWidget(prev_day_btn)
        date_line2.addWidget(today_btn)
        date_line2.addWidget(next_day_btn)
        date_line2.addStretch()

        date_main_layout.addLayout(date_line1)
        date_main_layout.addLayout(date_line2)

        layout.addWidget(date_group)
        
    def setup_global_kpi(self, layout):
        """Configure les KPI globaux avec une présentation claire et aérée."""
        self.kpi_group = QGroupBox("📊 Vue d'ensemble Production")
        self.kpi_group.setFont(QFont("Segoe UI", 10, QFont.Bold))
        self.kpi_group.setMinimumHeight(180)
        self.kpi_group.setMaximumHeight(220)
        kpi_layout = QVBoxLayout(self.kpi_group)
        kpi_layout.setSpacing(12)
        kpi_layout.setContentsMargins(15, 15, 15, 15)

        # === PRODUCTION TOTALE (GRANDE CARTE) ===
        total_card = QFrame()
        total_card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {SotramineTheme.PRIMARY},
                    stop:1 {SotramineTheme.PRIMARY_LIGHT});
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }}
        """)
        total_layout = QVBoxLayout(total_card)
        total_layout.setContentsMargins(15, 10, 15, 10)

        # Titre
        total_title = QLabel("🎯 PRODUCTION TOTALE DU JOUR")
        total_title.setStyleSheet("""
            color: white;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 5px;
        """)
        total_title.setAlignment(Qt.AlignCenter)

        # Valeur
        self.total_production_value = QLabel("0 T")
        self.total_production_value.setStyleSheet("""
            color: white;
            font-size: 28px;
            font-weight: bold;
        """)
        self.total_production_value.setAlignment(Qt.AlignCenter)

        total_layout.addWidget(total_title)
        total_layout.addWidget(self.total_production_value)
        kpi_layout.addWidget(total_card)

        # === DÉTAILS PAR UNITÉ (CARTES CÔTE À CÔTE) ===
        units_container = QFrame()
        units_main_layout = QHBoxLayout(units_container)
        units_main_layout.setSpacing(10)
        units_main_layout.setContentsMargins(0, 0, 0, 0)

        # CONCASSAGE
        crushing_card = QFrame()
        crushing_card.setStyleSheet(f"""
            QFrame {{
                background-color: {SotramineTheme.BG_CARD};
                border: 2px solid {SotramineTheme.ACCENT};
                border-radius: 8px;
                padding: 10px;
            }}
        """)
        crushing_layout = QVBoxLayout(crushing_card)
        crushing_layout.setContentsMargins(10, 8, 10, 8)

        crushing_title = QLabel("🔨 CONCASSAGE")
        crushing_title.setStyleSheet(f"""
            color: {SotramineTheme.ACCENT};
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 5px;
        """)
        crushing_title.setAlignment(Qt.AlignCenter)

        self.crushing_value = QLabel("0 T")
        self.crushing_value.setStyleSheet(f"""
            color: {SotramineTheme.TEXT_PRIMARY};
            font-size: 18px;
            font-weight: bold;
        """)
        self.crushing_value.setAlignment(Qt.AlignCenter)

        crushing_layout.addWidget(crushing_title)
        crushing_layout.addWidget(self.crushing_value)

        # LAVERIE
        washing_card = QFrame()
        washing_card.setStyleSheet(f"""
            QFrame {{
                background-color: {SotramineTheme.BG_CARD};
                border: 2px solid {SotramineTheme.SECONDARY};
                border-radius: 8px;
                padding: 10px;
            }}
        """)
        washing_layout = QVBoxLayout(washing_card)
        washing_layout.setContentsMargins(10, 8, 10, 8)

        washing_title = QLabel("💧 LAVERIE")
        washing_title.setStyleSheet(f"""
            color: {SotramineTheme.SECONDARY};
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 5px;
        """)
        washing_title.setAlignment(Qt.AlignCenter)

        self.washing_value = QLabel("0 T")
        self.washing_value.setStyleSheet(f"""
            color: {SotramineTheme.TEXT_PRIMARY};
            font-size: 18px;
            font-weight: bold;
        """)
        self.washing_value.setAlignment(Qt.AlignCenter)

        washing_layout.addWidget(washing_title)
        washing_layout.addWidget(self.washing_value)

        units_main_layout.addWidget(crushing_card)
        units_main_layout.addWidget(washing_card)
        kpi_layout.addWidget(units_container)

        layout.addWidget(self.kpi_group)
        
    def setup_unit_tabs(self, layout):
        """Configure les onglets pour chaque unité."""
        units_group = QGroupBox("🔧 Saisie par Unité")
        units_group.setFont(QFont("Segoe UI", 9, QFont.Bold))
        units_group.setMinimumHeight(350)  # Hauteur minimale
        units_group.setMaximumHeight(450)  # Hauteur maximale
        units_layout = QVBoxLayout(units_group)
        units_layout.setContentsMargins(8, 8, 8, 8)
        units_layout.setSpacing(5)

        # Onglets pour les unités
        self.unit_tabs = QTabWidget()
        self.unit_tabs.setTabPosition(QTabWidget.North)

        # Onglet Concassage
        crushing_widget = self.create_crushing_tab()
        self.unit_tabs.addTab(crushing_widget, "🔨 Concassage")

        # Onglet Laverie
        washing_widget = self.create_washing_tab()
        self.unit_tabs.addTab(washing_widget, "💧 Laverie")

        units_layout.addWidget(self.unit_tabs)
        layout.addWidget(units_group)
        
    def create_crushing_tab(self):
        """Crée l'onglet de saisie pour le concassage."""
        # Créer un widget avec défilement
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(6)
        layout.setContentsMargins(5, 5, 5, 5)

        # Formulaire de saisie concassage
        fields = [
            {
                'name': 'crushing_quantity_produced',
                'label': 'Produit (T)',
                'type': 'double',
                'suffix': 'T'
            },
            {
                'name': 'crushing_quantity_consumed',
                'label': 'Consommé (T)',
                'type': 'double',
                'suffix': 'T'
            },
            {
                'name': 'crushing_production_hours',
                'label': 'Heures (h)',
                'type': 'double',
                'suffix': 'h'
            },
            {
                'name': 'crushing_quality',
                'label': 'Qualité (%)',
                'type': 'double',
                'suffix': '%'
            }
        ]

        self.crushing_input = QuickInputWidget(fields)
        self.crushing_input.data_saved.connect(self.save_crushing_production)
        layout.addWidget(self.crushing_input)

        # Section consommations concassage (compacte)
        consumption_group = QGroupBox("Consommations")
        consumption_group.setMaximumHeight(80)
        consumption_layout = QFormLayout(consumption_group)
        consumption_layout.setContentsMargins(5, 5, 5, 5)
        consumption_layout.setSpacing(3)

        self.crushing_energy_input = QDoubleSpinBox()
        self.crushing_energy_input.setRange(0, 99999)
        self.crushing_energy_input.setSuffix(" kWh")
        self.crushing_energy_input.setMaximumWidth(120)
        consumption_layout.addRow("Énergie:", self.crushing_energy_input)

        layout.addWidget(consumption_group)
        layout.addStretch()

        # Configurer le défilement
        scroll_area.setWidget(widget)
        return scroll_area
        
    def create_washing_tab(self):
        """Crée l'onglet de saisie pour la laverie."""
        # Créer un widget avec défilement
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(6)
        layout.setContentsMargins(5, 5, 5, 5)

        # Formulaire de saisie laverie (libellés compacts)
        fields = [
            {
                'name': 'washing_quantity_produced',
                'label': 'Lavé (T)',
                'type': 'double',
                'suffix': 'T'
            },
            {
                'name': 'washing_quantity_consumed',
                'label': 'Consommé (T)',
                'type': 'double',
                'suffix': 'T'
            },
            {
                'name': 'washing_quality_input',
                'label': 'Qualité Entrée (%)',
                'type': 'double',
                'suffix': '%'
            },
            {
                'name': 'washing_quality_output',
                'label': 'Qualité Sortie (%)',
                'type': 'double',
                'suffix': '%'
            },
            {
                'name': 'washing_production_hours',
                'label': 'Heures (h)',
                'type': 'double',
                'suffix': 'h'
            }
        ]

        self.washing_input = QuickInputWidget(fields)
        self.washing_input.data_saved.connect(self.save_washing_production)
        layout.addWidget(self.washing_input)

        # Section consommations laverie (compacte)
        consumption_group = QGroupBox("Consommations")
        consumption_group.setMaximumHeight(120)
        consumption_layout = QFormLayout(consumption_group)
        consumption_layout.setContentsMargins(5, 5, 5, 5)
        consumption_layout.setSpacing(3)

        self.washing_water_input = QDoubleSpinBox()
        self.washing_water_input.setRange(0, 99999)
        self.washing_water_input.setSuffix(" m³")
        self.washing_water_input.setMaximumWidth(120)
        consumption_layout.addRow("Eau:", self.washing_water_input)

        self.washing_reactive_input = QDoubleSpinBox()
        self.washing_reactive_input.setRange(0, 99999)
        self.washing_reactive_input.setSuffix(" kg")
        self.washing_reactive_input.setMaximumWidth(120)
        consumption_layout.addRow("Réactifs:", self.washing_reactive_input)

        self.washing_energy_input = QDoubleSpinBox()
        self.washing_energy_input.setRange(0, 99999)
        self.washing_energy_input.setSuffix(" kWh")
        self.washing_energy_input.setMaximumWidth(120)
        consumption_layout.addRow("Énergie:", self.washing_energy_input)

        layout.addWidget(consumption_group)
        layout.addStretch()

        # Configurer le défilement
        scroll_area.setWidget(widget)
        return scroll_area

    def setup_downtime_section(self, layout):
        """Configure la section de gestion des arrêts."""
        downtime_group = QGroupBox("⏹️ Gestion des Arrêts")
        downtime_group.setFont(QFont("Segoe UI", 9, QFont.Bold))
        downtime_group.setMaximumHeight(220)  # Limiter la hauteur
        downtime_layout = QVBoxLayout(downtime_group)
        downtime_layout.setContentsMargins(8, 8, 8, 8)
        downtime_layout.setSpacing(5)

        # Formulaire d'ajout d'arrêt (compact)
        form_layout = QFormLayout()
        form_layout.setContentsMargins(0, 0, 0, 0)
        form_layout.setSpacing(5)

        # Première ligne : Unité et Durée
        line1_layout = QHBoxLayout()

        self.downtime_unit_combo = QComboBox()
        self.downtime_unit_combo.addItems(["Concassage", "Laverie"])
        self.downtime_unit_combo.setMaximumWidth(100)
        line1_layout.addWidget(QLabel("Unité:"))
        line1_layout.addWidget(self.downtime_unit_combo)

        self.downtime_duration_input = QSpinBox()
        self.downtime_duration_input.setRange(1, 9999)
        self.downtime_duration_input.setSuffix(" min")
        self.downtime_duration_input.setValue(30)
        self.downtime_duration_input.setMaximumWidth(80)
        line1_layout.addWidget(QLabel("Durée:"))
        line1_layout.addWidget(self.downtime_duration_input)
        line1_layout.addStretch()

        # Raison de l'arrêt
        self.downtime_reason_combo = QComboBox()
        self.downtime_reason_combo.setEditable(True)
        self.downtime_reason_combo.addItems([
            "Maintenance préventive",
            "Panne équipement",
            "Problème qualité",
            "Manque matière première",
            "Changement d'équipe",
            "Nettoyage",
            "Autre"
        ])

        # Description compacte
        self.downtime_description_input = QTextEdit()
        self.downtime_description_input.setMaximumHeight(40)
        self.downtime_description_input.setPlaceholderText("Description (optionnel)")

        # Bouton d'ajout
        add_downtime_btn = QPushButton("➕ Ajouter Arrêt")
        add_downtime_btn.setMaximumHeight(30)
        add_downtime_btn.clicked.connect(self.add_downtime)

        # Ajouter au layout
        downtime_layout.addLayout(line1_layout)
        form_layout.addRow("Raison:", self.downtime_reason_combo)
        form_layout.addRow("Description:", self.downtime_description_input)
        form_layout.addRow("", add_downtime_btn)

        downtime_layout.addLayout(form_layout)
        layout.addWidget(downtime_group)

    def setup_history_link(self, layout):
        """Configure la section de lien vers l'historique."""
        history_group = QGroupBox("📊 Consultation des Données")
        history_group.setFont(QFont("Segoe UI", 9, QFont.Bold))
        history_group.setMaximumHeight(80)
        history_layout = QVBoxLayout(history_group)
        history_layout.setContentsMargins(8, 8, 8, 8)

        # Message informatif
        info_label = QLabel("💡 Pour consulter l'historique détaillé, les analyses et les rapports,")
        info_label.setStyleSheet("color: #666; font-size: 11px;")
        history_layout.addWidget(info_label)

        info_label2 = QLabel("   rendez-vous dans l'onglet 'Historique Production'")
        info_label2.setStyleSheet("color: #666; font-size: 11px;")
        history_layout.addWidget(info_label2)

        layout.addWidget(history_group)



    def setup_timers(self):
        """Configure les timers pour les mises à jour automatiques."""
        # Timer pour les KPI
        self.kpi_timer = QTimer()
        self.kpi_timer.timeout.connect(self.update_global_kpi)
        self.kpi_timer.start(30000)  # 30 secondes

        # Timer pour les données (simplifié)
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self.update_global_kpi)
        self.data_timer.start(60000)  # 1 minute

    # === MÉTHODES DE NAVIGATION DE DATE ===

    def on_date_changed(self):
        """Appelée quand la date change."""
        self.update_kpi_title()
        self.check_existing_data()
        self.load_data_for_date()
        self.update_global_kpi()

    def update_kpi_title(self):
        """Met à jour le titre des KPI avec la date sélectionnée."""
        selected_date = self.production_date.date()
        formatted_date = format_date_for_display(selected_date.toPyDate())
        self.kpi_group.setTitle(f"📊 Vue d'ensemble Production - {formatted_date}")

    def go_to_previous_day(self):
        """Va au jour précédent."""
        current_date = self.production_date.date()
        previous_date = current_date.addDays(-1)
        self.production_date.setDate(previous_date)

    def go_to_next_day(self):
        """Va au jour suivant."""
        current_date = self.production_date.date()
        next_date = current_date.addDays(1)
        # Ne pas aller au-delà d'aujourd'hui
        if next_date <= QDate.currentDate():
            self.production_date.setDate(next_date)

    def go_to_today(self):
        """Va à aujourd'hui."""
        self.production_date.setDate(QDate.currentDate())

    def check_existing_data(self):
        """Vérifie si des données existent pour la date sélectionnée."""
        selected_date = self.production_date.date()
        start_datetime, end_datetime = get_date_range_for_day(selected_date)

        # Vérifier les productions
        crushing_productions = self.db_manager.get_production_summary(
            step=ProcessStepEnum.CONCASSAGE,
            start_date=start_datetime,
            end_date=end_datetime
        )

        washing_productions = self.db_manager.get_production_summary(
            step=ProcessStepEnum.LAVERIE,
            start_date=start_datetime,
            end_date=end_datetime
        )

        has_data = len(crushing_productions) > 0 or len(washing_productions) > 0

        if has_data:
            self.data_indicator.setText("📊 Données disponibles")
            self.data_indicator.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.data_indicator.setText("📝 Aucune donnée")
            self.data_indicator.setStyleSheet("color: orange; font-weight: bold;")

    def load_data_for_date(self):
        """Charge les données existantes pour la date sélectionnée."""
        selected_date = self.production_date.date()
        start_datetime, end_datetime = get_date_range_for_day(selected_date)

        # Charger les productions du concassage
        crushing_productions = self.db_manager.get_production_summary(
            step=ProcessStepEnum.CONCASSAGE,
            start_date=start_datetime,
            end_date=end_datetime
        )

        # Charger les productions de la laverie
        washing_productions = self.db_manager.get_production_summary(
            step=ProcessStepEnum.LAVERIE,
            start_date=start_datetime,
            end_date=end_datetime
        )

        # Pré-remplir les formulaires si des données existent
        if crushing_productions:
            latest_crushing = crushing_productions[-1]  # Dernière production
            self.prefill_crushing_form(latest_crushing)

        if washing_productions:
            latest_washing = washing_productions[-1]  # Dernière production
            self.prefill_washing_form(latest_washing)

    def prefill_crushing_form(self, production_data):
        """Pré-remplit le formulaire de concassage avec les données existantes."""
        try:
            # Pré-remplir les champs du formulaire
            if 'quantity' in production_data:
                self.crushing_input.fields['crushing_quantity_produced'].setValue(production_data['quantity'])
            if 'quantity_used' in production_data and production_data['quantity_used']:
                self.crushing_input.fields['crushing_quantity_consumed'].setValue(production_data['quantity_used'])
            if 'production_hours' in production_data and production_data['production_hours']:
                self.crushing_input.fields['crushing_production_hours'].setValue(production_data['production_hours'])
            if 'quality' in production_data and production_data['quality']:
                quality_str = str(production_data['quality']).replace('%', '').strip()
                try:
                    quality_val = float(quality_str)
                    self.crushing_input.fields['crushing_quality'].setValue(quality_val)
                except (ValueError, TypeError):
                    pass
        except Exception as e:
            logging.error(f"Erreur pré-remplissage concassage: {str(e)}")

    def prefill_washing_form(self, production_data):
        """Pré-remplit le formulaire de laverie avec les données existantes."""
        try:
            # Pré-remplir les champs du formulaire
            if 'quantity' in production_data:
                self.washing_input.fields['washing_quantity_produced'].setValue(production_data['quantity'])
            if 'quantity_used' in production_data and production_data['quantity_used']:
                self.washing_input.fields['washing_quantity_consumed'].setValue(production_data['quantity_used'])
            if 'production_hours' in production_data and production_data['production_hours']:
                self.washing_input.fields['washing_production_hours'].setValue(production_data['production_hours'])
            if 'quality' in production_data and production_data['quality']:
                quality_str = str(production_data['quality']).replace('%', '').strip()
                try:
                    quality_val = float(quality_str)
                    self.washing_input.fields['washing_quality_output'].setValue(quality_val)
                except (ValueError, TypeError):
                    pass
        except Exception as e:
            logging.error(f"Erreur pré-remplissage laverie: {str(e)}")

    # === MÉTHODES DE SAUVEGARDE ===

    def save_crushing_production(self, data):
        """Sauvegarde une production de concassage."""
        try:
            # Validation
            if not data.get('crushing_quantity_produced') or float(data['crushing_quantity_produced']) <= 0:
                QMessageBox.warning(self, "Erreur", "La quantité produite doit être positive")
                return

            quantity_produced = float(data['crushing_quantity_produced'])
            quantity_consumed = float(data.get('crushing_quantity_consumed', 0))
            hours = float(data.get('crushing_production_hours', 0))
            quality_str = data.get('crushing_quality', '30.0')

            # Convertir la qualité
            if quality_str and isinstance(quality_str, str):
                quality_str = quality_str.replace('%', '').strip()
            quality_value = float(quality_str) if quality_str else None

            # Utiliser la date sélectionnée (normalisée sans heure)
            selected_date = self.production_date.date()
            production_datetime = normalize_date(selected_date)

            # Enregistrer la production
            self.db_manager.add_production(
                step=ProcessStepEnum.CONCASSAGE,
                quantity=quantity_produced,
                quantity_used=quantity_consumed if quantity_consumed > 0 else None,
                production_hours=hours if hours > 0 else None,
                quality=f"{quality_value}%" if quality_value is not None else None,
                production_date=production_datetime
            )

            # Enregistrer la consommation d'énergie si saisie
            energy_consumption = self.crushing_energy_input.value()
            if energy_consumption > 0:
                self.db_manager.add_resource_consumption(
                    resource_type=ResourceType.ENERGY,
                    quantity=energy_consumption,
                    step=ProcessStepEnum.CONCASSAGE,
                    consumption_date=production_datetime
                )
                self.crushing_energy_input.setValue(0)

            # Effacer le formulaire
            self.crushing_input.clear_form()

            # Actualiser
            self.update_global_kpi()
            self.check_existing_data()

            QMessageBox.information(self, "✅ Succès",
                f"Production concassage de {quantity_produced:.1f}T enregistrée")

        except ValueError as e:
            error_msg = str(e)
            if "Stock insuffisant" in error_msg:
                QMessageBox.warning(self, "Stock Insuffisant", error_msg)
            else:
                QMessageBox.warning(self, "Erreur", "Veuillez saisir des valeurs numériques valides")
        except Exception as e:
            logging.error(f"Erreur sauvegarde production concassage: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")

    def save_washing_production(self, data):
        """Sauvegarde une production de laverie."""
        try:
            # Validation
            if not data.get('washing_quantity_produced') or float(data['washing_quantity_produced']) <= 0:
                QMessageBox.warning(self, "Erreur", "La quantité lavée doit être positive")
                return

            quantity_produced = float(data['washing_quantity_produced'])
            quantity_consumed = float(data.get('washing_quantity_consumed', 0))
            quality_output = data.get('washing_quality_output', '31.0')
            hours = float(data.get('washing_production_hours', 0))

            # Utiliser la date sélectionnée (normalisée sans heure)
            selected_date = self.production_date.date()
            production_datetime = normalize_date(selected_date)

            # Enregistrer la production
            self.db_manager.add_production(
                step=ProcessStepEnum.LAVERIE,
                quantity=quantity_produced,
                quantity_used=quantity_consumed if quantity_consumed > 0 else None,
                production_hours=hours if hours > 0 else None,
                quality=quality_output if quality_output else None,
                production_date=production_datetime
            )

            # Enregistrer les consommations de ressources
            water_used = self.washing_water_input.value()
            reactive_used = self.washing_reactive_input.value()
            energy_used = self.washing_energy_input.value()

            if water_used > 0:
                self.db_manager.add_resource_consumption(
                    resource_type=ResourceType.WATER,
                    quantity=water_used,
                    step=ProcessStepEnum.LAVERIE,
                    consumption_date=production_datetime
                )
                self.washing_water_input.setValue(0)

            if reactive_used > 0:
                self.db_manager.add_resource_consumption(
                    resource_type=ResourceType.REACTIVE,
                    quantity=reactive_used,
                    step=ProcessStepEnum.LAVERIE,
                    consumption_date=production_datetime
                )
                self.washing_reactive_input.setValue(0)

            if energy_used > 0:
                self.db_manager.add_resource_consumption(
                    resource_type=ResourceType.ENERGY,
                    quantity=energy_used,
                    step=ProcessStepEnum.LAVERIE,
                    consumption_date=production_datetime
                )
                self.washing_energy_input.setValue(0)

            # Effacer le formulaire
            self.washing_input.clear_form()

            # Actualiser
            self.update_global_kpi()
            self.check_existing_data()

            QMessageBox.information(self, "✅ Succès",
                f"Production laverie de {quantity_produced:.1f}T enregistrée")

        except ValueError:
            QMessageBox.warning(self, "Erreur", "Veuillez saisir des valeurs numériques valides")
        except Exception as e:
            logging.error(f"Erreur sauvegarde production laverie: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")

    def add_downtime(self):
        """Ajoute un arrêt de production."""
        try:
            unit = self.downtime_unit_combo.currentText()
            reason = self.downtime_reason_combo.currentText().strip()
            duration = self.downtime_duration_input.value()
            description = self.downtime_description_input.toPlainText().strip()

            if not reason:
                QMessageBox.warning(self, "Erreur", "Veuillez saisir une raison pour l'arrêt")
                return

            # Déterminer l'étape
            step = ProcessStepEnum.CONCASSAGE if unit == "Concassage" else ProcessStepEnum.LAVERIE

            # Utiliser la date sélectionnée (normalisée sans heure)
            selected_date = self.production_date.date()
            downtime_datetime = normalize_date(selected_date)

            # Enregistrer l'arrêt
            self.db_manager.add_downtime(
                step=step,
                reason=reason,
                duration=duration,
                description=description if description else None,
                downtime_date=downtime_datetime
            )

            # Effacer le formulaire
            self.downtime_reason_combo.setCurrentIndex(0)
            self.downtime_duration_input.setValue(30)
            self.downtime_description_input.clear()

            # Actualiser
            self.update_global_kpi()

            QMessageBox.information(self, "✅ Succès",
                f"Arrêt de {duration} min enregistré pour {unit}")

        except Exception as e:
            logging.error(f"Erreur ajout arrêt: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")

    # === MÉTHODES DE MISE À JOUR ===

    def update_global_kpi(self):
        """Met à jour les KPI globaux."""
        try:
            # Utiliser la date sélectionnée
            selected_date = self.production_date.date()
            start_datetime, end_datetime = get_date_range_for_day(selected_date)

            # Production concassage du jour sélectionné
            crushing_productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.CONCASSAGE,
                start_date=start_datetime,
                end_date=end_datetime
            )
            crushing_total = sum(p['quantity'] for p in crushing_productions)
            self.crushing_value.setText(f"{crushing_total:.1f} T")

            # Production laverie du jour sélectionné
            washing_productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.LAVERIE,
                start_date=start_datetime,
                end_date=end_datetime
            )
            washing_total = sum(p['quantity'] for p in washing_productions)
            self.washing_value.setText(f"{washing_total:.1f} T")

            # Production totale = production de la laverie (produit final)
            total_production = washing_total
            self.total_production_value.setText(f"{total_production:.1f} T")

            # Interface simplifiée - calculs d'efficacité et qualité supprimés
            # pour réduire l'encombrement de la vue d'ensemble

        except Exception as e:
            logging.error(f"Erreur mise à jour KPI globaux: {str(e)}")

    def refresh_all_data(self):
        """Actualise toutes les données."""
        self.update_global_kpi()

    def refresh_tab(self):
        """Méthode appelée lors du changement d'onglet."""
        self.refresh_all_data()
