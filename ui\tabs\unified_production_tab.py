"""
Onglet unifié de production regroupant les unités de concassage et laverie
avec leurs consommations et arrêts.
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QMessageBox, QTabWidget,
                            QGroupBox, QLabel, QGridLayout, QFrame, QSplitter,
                            QScrollArea, QWidget, QPushButton, QComboBox, QSpinBox,
                            QDoubleSpinBox, QDateEdit, QTextEdit, QFormLayout)
from PyQt5.QtCore import Qt, QDate, QTimer, pyqtSignal
from PyQt5.QtGui import QFont
from datetime import datetime, timedelta, date
import logging

from .base_optimized import BaseOptimizedTab, QuickInputWidget, StatusWidget
from ui.widgets.professional_widgets import KPILine, StatusIndicator, ActionButton, ProfessionalTable
from models.enums import ProcessStepEnum, StockMovementType, ResourceType
from ui.professional_theme import SotramineTheme
from utils.date_utils import normalize_date, get_date_range_for_day, format_date_for_display

class UnifiedProductionTab(BaseOptimizedTab):
    """Onglet unifié pour la gestion de la production des deux unités."""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "🏭 Production Unifiée", parent)
        self.setup_interface()
        self.setup_timers()
        
    def setup_interface(self):
        """Configure l'interface unifiée simplifiée avec défilement."""
        # Zone unique avec défilement pour les contrôles de saisie
        main_scroll = QScrollArea()
        main_scroll.setWidgetResizable(True)
        main_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        main_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.main_layout.addWidget(main_scroll)

        # Widget contenu principal
        main_content = QWidget()
        main_layout = QVBoxLayout(main_content)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # Configurer la zone de défilement
        main_scroll.setWidget(main_content)

        # === CONFIGURATION DU PANNEAU PRINCIPAL ===
        self.setup_main_panel(main_layout)
        
        # Charger les données initiales
        self.update_kpi_title()
        self.check_existing_data()
        self.load_data_for_date()
        self.refresh_all_data()
        
    def setup_main_panel(self, layout):
        """Configure le panneau principal avec KPI et saisie."""
        # Section sélection de date
        self.setup_date_selection(layout)

        # Section KPI globale
        self.setup_global_kpi(layout)

        # Onglets pour les unités
        self.setup_unit_tabs(layout)

        # Section arrêts
        self.setup_downtime_section(layout)

        # Section lien vers historique
        self.setup_history_link(layout)

        # Espacement final pour éviter l'encombrement
        layout.addStretch()

    def setup_date_selection(self, layout):
        """Configure une sélection de date professionnelle et bien organisée."""

        # === CONTENEUR PRINCIPAL AVEC TITRE ===
        date_group = QGroupBox("📅 Sélection de Date")
        date_group.setFont(QFont("Segoe UI", 12, QFont.Bold))
        date_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 14px;
                font-weight: bold;
                color: {SotramineTheme.PRIMARY};
                border: 2px solid {SotramineTheme.PRIMARY};
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: {SotramineTheme.BG_SECONDARY};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: {SotramineTheme.BG_PRIMARY};
                border-radius: 4px;
            }}
        """)
        date_group.setMinimumHeight(110)
        date_group.setMaximumHeight(130)

        date_layout = QVBoxLayout(date_group)
        date_layout.setContentsMargins(20, 20, 20, 15)
        date_layout.setSpacing(12)

        # === LIGNE 1: SÉLECTEUR ET INDICATEUR ===
        top_line = QHBoxLayout()
        top_line.setSpacing(15)

        # Label date
        date_label = QLabel("Date de production :")
        date_label.setStyleSheet(f"""
            font-size: 13px;
            font-weight: bold;
            color: {SotramineTheme.TEXT_PRIMARY};
            padding: 5px;
        """)

        # Sélecteur de date professionnel
        self.production_date = QDateEdit()
        self.production_date.setCalendarPopup(True)
        self.production_date.setDate(QDate.currentDate())
        self.production_date.setDisplayFormat("dd/MM/yyyy")
        self.production_date.dateChanged.connect(self.on_date_changed)
        self.production_date.setMinimumWidth(150)
        self.production_date.setMinimumHeight(35)
        self.production_date.setStyleSheet(f"""
            QDateEdit {{
                font-size: 14px;
                font-weight: bold;
                padding: 8px 12px;
                border: 2px solid {SotramineTheme.GRAY_400};
                border-radius: 8px;
                background-color: white;
                color: {SotramineTheme.TEXT_PRIMARY};
            }}
            QDateEdit:focus {{
                border-color: {SotramineTheme.PRIMARY};
                background-color: {SotramineTheme.GRAY_50};
            }}
            QDateEdit::drop-down {{
                border: none;
                width: 20px;
            }}
        """)

        # Indicateur de données
        self.data_indicator = QLabel("📊 Données disponibles")
        self.data_indicator.setStyleSheet(f"""
            color: white;
            font-weight: bold;
            font-size: 12px;
            padding: 8px 12px;
            background-color: {SotramineTheme.SECONDARY};
            border-radius: 6px;
        """)
        self.data_indicator.setMinimumHeight(35)

        top_line.addWidget(date_label)
        top_line.addWidget(self.production_date)
        top_line.addStretch()
        top_line.addWidget(self.data_indicator)

        # === LIGNE 2: BOUTONS DE NAVIGATION ===
        buttons_line = QHBoxLayout()
        buttons_line.setSpacing(12)

        # Bouton Précédent
        prev_day_btn = QPushButton("◀ Précédent")
        prev_day_btn.setMinimumWidth(110)
        prev_day_btn.setMinimumHeight(32)
        prev_day_btn.setStyleSheet(f"""
            QPushButton {{
                font-size: 12px;
                font-weight: bold;
                padding: 8px 15px;
                border: 2px solid {SotramineTheme.GRAY_400};
                border-radius: 6px;
                background-color: white;
                color: {SotramineTheme.TEXT_PRIMARY};
            }}
            QPushButton:hover {{
                background-color: {SotramineTheme.GRAY_100};
                border-color: {SotramineTheme.PRIMARY};
                color: {SotramineTheme.PRIMARY};
            }}
            QPushButton:pressed {{
                background-color: {SotramineTheme.GRAY_200};
            }}
        """)
        prev_day_btn.clicked.connect(self.go_to_previous_day)

        # Bouton Aujourd'hui
        today_btn = QPushButton("Aujourd'hui")
        today_btn.setMinimumWidth(110)
        today_btn.setMinimumHeight(32)
        today_btn.setStyleSheet(f"""
            QPushButton {{
                font-size: 12px;
                font-weight: bold;
                padding: 8px 15px;
                border: 2px solid {SotramineTheme.PRIMARY};
                border-radius: 6px;
                background-color: {SotramineTheme.PRIMARY};
                color: white;
            }}
            QPushButton:hover {{
                background-color: {SotramineTheme.PRIMARY_DARK};
            }}
            QPushButton:pressed {{
                background-color: {SotramineTheme.PRIMARY_DARK};
            }}
        """)
        today_btn.clicked.connect(self.go_to_today)

        # Bouton Suivant
        next_day_btn = QPushButton("Suivant ▶")
        next_day_btn.setMinimumWidth(110)
        next_day_btn.setMinimumHeight(32)
        next_day_btn.setStyleSheet(f"""
            QPushButton {{
                font-size: 12px;
                font-weight: bold;
                padding: 8px 15px;
                border: 2px solid {SotramineTheme.GRAY_400};
                border-radius: 6px;
                background-color: white;
                color: {SotramineTheme.TEXT_PRIMARY};
            }}
            QPushButton:hover {{
                background-color: {SotramineTheme.GRAY_100};
                border-color: {SotramineTheme.PRIMARY};
                color: {SotramineTheme.PRIMARY};
            }}
            QPushButton:pressed {{
                background-color: {SotramineTheme.GRAY_200};
            }}
        """)
        next_day_btn.clicked.connect(self.go_to_next_day)

        buttons_line.addWidget(prev_day_btn)
        buttons_line.addWidget(today_btn)
        buttons_line.addWidget(next_day_btn)
        buttons_line.addStretch()

        date_layout.addLayout(top_line)
        date_layout.addLayout(buttons_line)
        layout.addWidget(date_group)
        
    def setup_global_kpi(self, layout):
        """Configure une vue d'ensemble professionnelle et bien organisée."""

        # === CONTENEUR PRINCIPAL AVEC TITRE ===
        main_container = QGroupBox("📊 Vue d'Ensemble Production")
        main_container.setFont(QFont("Segoe UI", 14, QFont.Bold))
        main_container.setStyleSheet(f"""
            QGroupBox {{
                font-size: 16px;
                font-weight: bold;
                color: {SotramineTheme.PRIMARY};
                border: 3px solid {SotramineTheme.PRIMARY};
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 20px;
                background-color: {SotramineTheme.BG_SECONDARY};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                background-color: {SotramineTheme.BG_PRIMARY};
                border-radius: 6px;
            }}
        """)
        main_container.setMinimumHeight(300)  # Réduit de 400 à 300

        container_layout = QVBoxLayout(main_container)
        container_layout.setSpacing(25)
        container_layout.setContentsMargins(25, 30, 25, 25)

        # === SECTION 1: PRODUCTION TOTALE ===
        total_section = QFrame()
        total_section.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {SotramineTheme.PRIMARY},
                    stop:1 {SotramineTheme.PRIMARY_LIGHT});
                border-radius: 12px;
                border: 2px solid {SotramineTheme.PRIMARY_DARK};
            }}
        """)
        total_section.setMinimumHeight(90)  # Réduit de 120 à 90

        total_layout = QHBoxLayout(total_section)  # Changé en HBoxLayout
        total_layout.setContentsMargins(20, 10, 20, 10)  # Marges réduites
        total_layout.setSpacing(15)

        # Titre section à gauche, plus petit
        total_header = QLabel("🎯 Production Totale")  # Titre raccourci
        total_header.setStyleSheet("""
            color: white;
            font-size: 14px;
            font-weight: bold;
            padding: 5px;
        """)
        total_header.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)

        # Valeur principale à droite, plus visible
        self.total_production_value = QLabel("0 T")
        self.total_production_value.setStyleSheet("""
            color: white;
            font-size: 32px;
            font-weight: bold;
            padding: 8px;
        """)
        self.total_production_value.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        total_layout.addWidget(total_header)
        total_layout.addStretch()  # Espace entre titre et valeur
        total_layout.addWidget(self.total_production_value)
        container_layout.addWidget(total_section)

        # === SECTION 2: DÉTAIL PAR UNITÉ - ARCHITECTURE TABLEAU ===
        units_section = QFrame()
        units_section.setStyleSheet(f"""
            QFrame {{
                background-color: {SotramineTheme.BG_PRIMARY};
                border: 2px solid {SotramineTheme.GRAY_300};
                border-radius: 10px;
                padding: 12px;
            }}
        """)
        units_section.setMinimumHeight(120)

        units_main_layout = QVBoxLayout(units_section)
        units_main_layout.setSpacing(10)
        units_main_layout.setContentsMargins(12, 10, 12, 10)

        # === TABLEAU STYLE PROFESSIONNEL ===
        table_container = QFrame()
        table_container.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid {SotramineTheme.GRAY_300};
                border-radius: 8px;
            }}
        """)

        table_layout = QVBoxLayout(table_container)
        table_layout.setSpacing(0)
        table_layout.setContentsMargins(0, 0, 0, 0)

        # En-tête du tableau
        header_row = QFrame()
        header_row.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {SotramineTheme.PRIMARY},
                    stop:1 {SotramineTheme.PRIMARY_LIGHT});
                border-radius: 8px 8px 0 0;
                padding: 8px;
            }}
        """)
        header_row.setMinimumHeight(35)

        header_layout = QHBoxLayout(header_row)
        header_layout.setContentsMargins(15, 5, 15, 5)

        # Colonnes d'en-tête
        unit_header = QLabel("UNITÉ")
        unit_header.setStyleSheet("color: white; font-size: 12px; font-weight: bold;")
        unit_header.setMinimumWidth(100)

        production_header = QLabel("PRODUCTION")
        production_header.setStyleSheet("color: white; font-size: 12px; font-weight: bold;")
        production_header.setAlignment(Qt.AlignCenter)

        header_layout.addWidget(unit_header)
        header_layout.addStretch()
        header_layout.addWidget(production_header)

        table_layout.addWidget(header_row)

        # === LIGNE CONCASSAGE ===
        crushing_row = QFrame()
        crushing_row.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border-bottom: 1px solid {SotramineTheme.GRAY_200};
                padding: 8px;
            }}
            QFrame:hover {{
                background-color: {SotramineTheme.GRAY_50};
            }}
        """)
        crushing_row.setMinimumHeight(40)

        crushing_row_layout = QHBoxLayout(crushing_row)
        crushing_row_layout.setContentsMargins(15, 8, 15, 8)

        # Nom de l'unité avec icône
        crushing_unit = QFrame()
        crushing_unit_layout = QHBoxLayout(crushing_unit)
        crushing_unit_layout.setContentsMargins(0, 0, 0, 0)
        crushing_unit_layout.setSpacing(8)

        crushing_icon = QLabel("🔨")
        crushing_icon.setStyleSheet("font-size: 18px;")
        crushing_name = QLabel("Concassage")
        crushing_name.setStyleSheet(f"""
            color: {SotramineTheme.ACCENT};
            font-size: 14px;
            font-weight: bold;
        """)

        crushing_unit_layout.addWidget(crushing_icon)
        crushing_unit_layout.addWidget(crushing_name)
        crushing_unit_layout.addStretch()

        # Valeur production
        self.crushing_value = QLabel("0 T")
        self.crushing_value.setStyleSheet(f"""
            color: {SotramineTheme.TEXT_PRIMARY};
            font-size: 20px;
            font-weight: bold;
            padding: 5px 15px;
            background-color: {SotramineTheme.ACCENT};
            color: white;
            border-radius: 6px;
            min-width: 80px;
        """)
        self.crushing_value.setAlignment(Qt.AlignCenter)

        crushing_row_layout.addWidget(crushing_unit)
        crushing_row_layout.addStretch()
        crushing_row_layout.addWidget(self.crushing_value)

        table_layout.addWidget(crushing_row)

        # === LIGNE LAVERIE ===
        washing_row = QFrame()
        washing_row.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border-radius: 0 0 8px 8px;
                padding: 8px;
            }}
            QFrame:hover {{
                background-color: {SotramineTheme.GRAY_50};
            }}
        """)
        washing_row.setMinimumHeight(40)

        washing_row_layout = QHBoxLayout(washing_row)
        washing_row_layout.setContentsMargins(15, 8, 15, 8)

        # Nom de l'unité avec icône
        washing_unit = QFrame()
        washing_unit_layout = QHBoxLayout(washing_unit)
        washing_unit_layout.setContentsMargins(0, 0, 0, 0)
        washing_unit_layout.setSpacing(8)

        washing_icon = QLabel("💧")
        washing_icon.setStyleSheet("font-size: 18px;")
        washing_name = QLabel("Laverie")
        washing_name.setStyleSheet(f"""
            color: {SotramineTheme.SECONDARY};
            font-size: 14px;
            font-weight: bold;
        """)

        washing_unit_layout.addWidget(washing_icon)
        washing_unit_layout.addWidget(washing_name)
        washing_unit_layout.addStretch()

        # Valeur production
        self.washing_value = QLabel("0 T")
        self.washing_value.setStyleSheet(f"""
            color: {SotramineTheme.TEXT_PRIMARY};
            font-size: 20px;
            font-weight: bold;
            padding: 5px 15px;
            background-color: {SotramineTheme.SECONDARY};
            color: white;
            border-radius: 6px;
            min-width: 80px;
        """)
        self.washing_value.setAlignment(Qt.AlignCenter)

        washing_row_layout.addWidget(washing_unit)
        washing_row_layout.addStretch()
        washing_row_layout.addWidget(self.washing_value)

        table_layout.addWidget(washing_row)

        units_main_layout.addWidget(table_container)
        container_layout.addWidget(units_section)

        layout.addWidget(main_container)
        
    def setup_unit_tabs(self, layout):
        """Configure les onglets pour chaque unité."""
        units_group = QGroupBox("🔧 Saisie par Unité")
        units_group.setFont(QFont("Segoe UI", 9, QFont.Bold))
        units_group.setMinimumHeight(350)  # Hauteur minimale
        units_group.setMaximumHeight(450)  # Hauteur maximale
        units_layout = QVBoxLayout(units_group)
        units_layout.setContentsMargins(8, 8, 8, 8)
        units_layout.setSpacing(5)

        # Onglets pour les unités
        self.unit_tabs = QTabWidget()
        self.unit_tabs.setTabPosition(QTabWidget.North)

        # Onglet Concassage
        crushing_widget = self.create_crushing_tab()
        self.unit_tabs.addTab(crushing_widget, "🔨 Concassage")

        # Onglet Laverie
        washing_widget = self.create_washing_tab()
        self.unit_tabs.addTab(washing_widget, "💧 Laverie")

        units_layout.addWidget(self.unit_tabs)
        layout.addWidget(units_group)
        
    def create_crushing_tab(self):
        """Crée l'onglet de saisie pour le concassage."""
        # Créer un widget avec défilement
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(6)
        layout.setContentsMargins(5, 5, 5, 5)

        # Formulaire de saisie concassage
        fields = [
            {
                'name': 'crushing_quantity_produced',
                'label': 'Produit (T)',
                'type': 'double',
                'suffix': 'T'
            },
            {
                'name': 'crushing_quantity_consumed',
                'label': 'Consommé (T)',
                'type': 'double',
                'suffix': 'T'
            },
            {
                'name': 'crushing_production_hours',
                'label': 'Heures (h)',
                'type': 'double',
                'suffix': 'h'
            },
            {
                'name': 'crushing_quality',
                'label': 'Qualité (%)',
                'type': 'double',
                'suffix': '%'
            }
        ]

        self.crushing_input = QuickInputWidget(fields)
        self.crushing_input.data_saved.connect(self.save_crushing_production)
        layout.addWidget(self.crushing_input)

        # Section consommations concassage (compacte)
        consumption_group = QGroupBox("Consommations")
        consumption_group.setMaximumHeight(80)
        consumption_layout = QFormLayout(consumption_group)
        consumption_layout.setContentsMargins(5, 5, 5, 5)
        consumption_layout.setSpacing(3)

        self.crushing_energy_input = QDoubleSpinBox()
        self.crushing_energy_input.setRange(0, 99999)
        self.crushing_energy_input.setSuffix(" kWh")
        self.crushing_energy_input.setMaximumWidth(120)
        consumption_layout.addRow("Énergie:", self.crushing_energy_input)

        layout.addWidget(consumption_group)
        layout.addStretch()

        # Configurer le défilement
        scroll_area.setWidget(widget)
        return scroll_area
        
    def create_washing_tab(self):
        """Crée l'onglet de saisie pour la laverie."""
        # Créer un widget avec défilement
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(6)
        layout.setContentsMargins(5, 5, 5, 5)

        # Formulaire de saisie laverie (libellés compacts)
        fields = [
            {
                'name': 'washing_quantity_produced',
                'label': 'Lavé (T)',
                'type': 'double',
                'suffix': 'T'
            },
            {
                'name': 'washing_quantity_consumed',
                'label': 'Consommé (T)',
                'type': 'double',
                'suffix': 'T'
            },
            {
                'name': 'washing_quality_input',
                'label': 'Qualité Entrée (%)',
                'type': 'double',
                'suffix': '%'
            },
            {
                'name': 'washing_quality_output',
                'label': 'Qualité Sortie (%)',
                'type': 'double',
                'suffix': '%'
            },
            {
                'name': 'washing_production_hours',
                'label': 'Heures (h)',
                'type': 'double',
                'suffix': 'h'
            }
        ]

        self.washing_input = QuickInputWidget(fields)
        self.washing_input.data_saved.connect(self.save_washing_production)
        layout.addWidget(self.washing_input)

        # Section consommations laverie (compacte)
        consumption_group = QGroupBox("Consommations")
        consumption_group.setMaximumHeight(120)
        consumption_layout = QFormLayout(consumption_group)
        consumption_layout.setContentsMargins(5, 5, 5, 5)
        consumption_layout.setSpacing(3)

        self.washing_water_input = QDoubleSpinBox()
        self.washing_water_input.setRange(0, 99999)
        self.washing_water_input.setSuffix(" m³")
        self.washing_water_input.setMaximumWidth(120)
        consumption_layout.addRow("Eau:", self.washing_water_input)

        self.washing_reactive_input = QDoubleSpinBox()
        self.washing_reactive_input.setRange(0, 99999)
        self.washing_reactive_input.setSuffix(" kg")
        self.washing_reactive_input.setMaximumWidth(120)
        consumption_layout.addRow("Réactifs:", self.washing_reactive_input)

        self.washing_energy_input = QDoubleSpinBox()
        self.washing_energy_input.setRange(0, 99999)
        self.washing_energy_input.setSuffix(" kWh")
        self.washing_energy_input.setMaximumWidth(120)
        consumption_layout.addRow("Énergie:", self.washing_energy_input)

        layout.addWidget(consumption_group)
        layout.addStretch()

        # Configurer le défilement
        scroll_area.setWidget(widget)
        return scroll_area

    def setup_downtime_section(self, layout):
        """Configure la section de gestion des arrêts."""
        downtime_group = QGroupBox("⏹️ Gestion des Arrêts")
        downtime_group.setFont(QFont("Segoe UI", 9, QFont.Bold))
        downtime_group.setMaximumHeight(220)  # Limiter la hauteur
        downtime_layout = QVBoxLayout(downtime_group)
        downtime_layout.setContentsMargins(8, 8, 8, 8)
        downtime_layout.setSpacing(5)

        # Formulaire d'ajout d'arrêt (compact)
        form_layout = QFormLayout()
        form_layout.setContentsMargins(0, 0, 0, 0)
        form_layout.setSpacing(5)

        # Première ligne : Unité et Durée
        line1_layout = QHBoxLayout()

        self.downtime_unit_combo = QComboBox()
        self.downtime_unit_combo.addItems(["Concassage", "Laverie"])
        self.downtime_unit_combo.setMaximumWidth(100)
        line1_layout.addWidget(QLabel("Unité:"))
        line1_layout.addWidget(self.downtime_unit_combo)

        self.downtime_duration_input = QSpinBox()
        self.downtime_duration_input.setRange(1, 9999)
        self.downtime_duration_input.setSuffix(" min")
        self.downtime_duration_input.setValue(30)
        self.downtime_duration_input.setMaximumWidth(80)
        line1_layout.addWidget(QLabel("Durée:"))
        line1_layout.addWidget(self.downtime_duration_input)
        line1_layout.addStretch()

        # Raison de l'arrêt
        self.downtime_reason_combo = QComboBox()
        self.downtime_reason_combo.setEditable(True)
        self.downtime_reason_combo.addItems([
            "Maintenance préventive",
            "Panne équipement",
            "Problème qualité",
            "Manque matière première",
            "Changement d'équipe",
            "Nettoyage",
            "Autre"
        ])

        # Description compacte
        self.downtime_description_input = QTextEdit()
        self.downtime_description_input.setMaximumHeight(40)
        self.downtime_description_input.setPlaceholderText("Description (optionnel)")

        # Bouton d'ajout
        add_downtime_btn = QPushButton("➕ Ajouter Arrêt")
        add_downtime_btn.setMaximumHeight(30)
        add_downtime_btn.clicked.connect(self.add_downtime)

        # Ajouter au layout
        downtime_layout.addLayout(line1_layout)
        form_layout.addRow("Raison:", self.downtime_reason_combo)
        form_layout.addRow("Description:", self.downtime_description_input)
        form_layout.addRow("", add_downtime_btn)

        downtime_layout.addLayout(form_layout)
        layout.addWidget(downtime_group)

    def setup_history_link(self, layout):
        """Configure la section de lien vers l'historique."""
        history_group = QGroupBox("📊 Consultation des Données")
        history_group.setFont(QFont("Segoe UI", 9, QFont.Bold))
        history_group.setMaximumHeight(80)
        history_layout = QVBoxLayout(history_group)
        history_layout.setContentsMargins(8, 8, 8, 8)

        # Message informatif
        info_label = QLabel("💡 Pour consulter l'historique détaillé, les analyses et les rapports,")
        info_label.setStyleSheet("color: #666; font-size: 11px;")
        history_layout.addWidget(info_label)

        info_label2 = QLabel("   rendez-vous dans l'onglet 'Historique Production'")
        info_label2.setStyleSheet("color: #666; font-size: 11px;")
        history_layout.addWidget(info_label2)

        layout.addWidget(history_group)



    def setup_timers(self):
        """Configure les timers pour les mises à jour automatiques."""
        # Timer pour les KPI
        self.kpi_timer = QTimer()
        self.kpi_timer.timeout.connect(self.update_global_kpi)
        self.kpi_timer.start(30000)  # 30 secondes

        # Timer pour les données (simplifié)
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self.update_global_kpi)
        self.data_timer.start(60000)  # 1 minute

    # === MÉTHODES DE NAVIGATION DE DATE ===

    def on_date_changed(self):
        """Appelée quand la date change."""
        self.update_kpi_title()
        self.check_existing_data()
        self.load_data_for_date()
        self.update_global_kpi()

    def update_kpi_title(self):
        """Met à jour le titre des KPI avec la date sélectionnée (méthode simplifiée)."""
        # Plus besoin de mettre à jour le titre car l'interface est simplifiée
        pass

    def go_to_previous_day(self):
        """Va au jour précédent."""
        current_date = self.production_date.date()
        previous_date = current_date.addDays(-1)
        self.production_date.setDate(previous_date)

    def go_to_next_day(self):
        """Va au jour suivant."""
        current_date = self.production_date.date()
        next_date = current_date.addDays(1)
        # Ne pas aller au-delà d'aujourd'hui
        if next_date <= QDate.currentDate():
            self.production_date.setDate(next_date)

    def go_to_today(self):
        """Va à aujourd'hui."""
        self.production_date.setDate(QDate.currentDate())

    def check_existing_data(self):
        """Vérifie si des données existent pour la date sélectionnée."""
        selected_date = self.production_date.date()
        start_datetime, end_datetime = get_date_range_for_day(selected_date)

        # Vérifier les productions
        crushing_productions = self.db_manager.get_production_summary(
            step=ProcessStepEnum.CONCASSAGE,
            start_date=start_datetime,
            end_date=end_datetime
        )

        washing_productions = self.db_manager.get_production_summary(
            step=ProcessStepEnum.LAVERIE,
            start_date=start_datetime,
            end_date=end_datetime
        )

        has_data = len(crushing_productions) > 0 or len(washing_productions) > 0

        if has_data:
            self.data_indicator.setText("📊 Données disponibles")
            self.data_indicator.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.data_indicator.setText("📝 Aucune donnée")
            self.data_indicator.setStyleSheet("color: orange; font-weight: bold;")

    def load_data_for_date(self):
        """Charge les données existantes pour la date sélectionnée."""
        selected_date = self.production_date.date()
        start_datetime, end_datetime = get_date_range_for_day(selected_date)

        # Charger les productions du concassage
        crushing_productions = self.db_manager.get_production_summary(
            step=ProcessStepEnum.CONCASSAGE,
            start_date=start_datetime,
            end_date=end_datetime
        )

        # Charger les productions de la laverie
        washing_productions = self.db_manager.get_production_summary(
            step=ProcessStepEnum.LAVERIE,
            start_date=start_datetime,
            end_date=end_datetime
        )

        # Pré-remplir les formulaires si des données existent
        if crushing_productions:
            latest_crushing = crushing_productions[-1]  # Dernière production
            self.prefill_crushing_form(latest_crushing)

        if washing_productions:
            latest_washing = washing_productions[-1]  # Dernière production
            self.prefill_washing_form(latest_washing)

    def prefill_crushing_form(self, production_data):
        """Pré-remplit le formulaire de concassage avec les données existantes."""
        try:
            # Pré-remplir les champs du formulaire
            if 'quantity' in production_data:
                self.crushing_input.fields['crushing_quantity_produced'].setValue(production_data['quantity'])
            if 'quantity_used' in production_data and production_data['quantity_used']:
                self.crushing_input.fields['crushing_quantity_consumed'].setValue(production_data['quantity_used'])
            if 'production_hours' in production_data and production_data['production_hours']:
                self.crushing_input.fields['crushing_production_hours'].setValue(production_data['production_hours'])
            if 'quality' in production_data and production_data['quality']:
                quality_str = str(production_data['quality']).replace('%', '').strip()
                try:
                    quality_val = float(quality_str)
                    self.crushing_input.fields['crushing_quality'].setValue(quality_val)
                except (ValueError, TypeError):
                    pass
        except Exception as e:
            logging.error(f"Erreur pré-remplissage concassage: {str(e)}")

    def prefill_washing_form(self, production_data):
        """Pré-remplit le formulaire de laverie avec les données existantes."""
        try:
            # Pré-remplir les champs du formulaire
            if 'quantity' in production_data:
                self.washing_input.fields['washing_quantity_produced'].setValue(production_data['quantity'])
            if 'quantity_used' in production_data and production_data['quantity_used']:
                self.washing_input.fields['washing_quantity_consumed'].setValue(production_data['quantity_used'])
            if 'production_hours' in production_data and production_data['production_hours']:
                self.washing_input.fields['washing_production_hours'].setValue(production_data['production_hours'])
            if 'quality' in production_data and production_data['quality']:
                quality_str = str(production_data['quality']).replace('%', '').strip()
                try:
                    quality_val = float(quality_str)
                    self.washing_input.fields['washing_quality_output'].setValue(quality_val)
                except (ValueError, TypeError):
                    pass
        except Exception as e:
            logging.error(f"Erreur pré-remplissage laverie: {str(e)}")

    # === MÉTHODES DE SAUVEGARDE ===

    def save_crushing_production(self, data):
        """Sauvegarde une production de concassage."""
        try:
            # Validation
            if not data.get('crushing_quantity_produced') or float(data['crushing_quantity_produced']) <= 0:
                QMessageBox.warning(self, "Erreur", "La quantité produite doit être positive")
                return

            quantity_produced = float(data['crushing_quantity_produced'])
            quantity_consumed = float(data.get('crushing_quantity_consumed', 0))
            hours = float(data.get('crushing_production_hours', 0))
            quality_str = data.get('crushing_quality', '30.0')

            # Convertir la qualité
            if quality_str and isinstance(quality_str, str):
                quality_str = quality_str.replace('%', '').strip()
            quality_value = float(quality_str) if quality_str else None

            # Utiliser la date sélectionnée (normalisée sans heure)
            selected_date = self.production_date.date()
            production_datetime = normalize_date(selected_date)

            # Enregistrer la production
            self.db_manager.add_production(
                step=ProcessStepEnum.CONCASSAGE,
                quantity=quantity_produced,
                quantity_used=quantity_consumed if quantity_consumed > 0 else None,
                production_hours=hours if hours > 0 else None,
                quality=f"{quality_value}%" if quality_value is not None else None,
                production_date=production_datetime
            )

            # Enregistrer la consommation d'énergie si saisie
            energy_consumption = self.crushing_energy_input.value()
            if energy_consumption > 0:
                self.db_manager.add_resource_consumption(
                    resource_type=ResourceType.ENERGY,
                    quantity=energy_consumption,
                    step=ProcessStepEnum.CONCASSAGE,
                    consumption_date=production_datetime
                )
                self.crushing_energy_input.setValue(0)

            # Effacer le formulaire
            self.crushing_input.clear_form()

            # Actualiser
            self.update_global_kpi()
            self.check_existing_data()

            QMessageBox.information(self, "✅ Succès",
                f"Production concassage de {quantity_produced:.1f}T enregistrée")

        except ValueError as e:
            error_msg = str(e)
            if "Stock insuffisant" in error_msg:
                QMessageBox.warning(self, "Stock Insuffisant", error_msg)
            else:
                QMessageBox.warning(self, "Erreur", "Veuillez saisir des valeurs numériques valides")
        except Exception as e:
            logging.error(f"Erreur sauvegarde production concassage: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")

    def save_washing_production(self, data):
        """Sauvegarde une production de laverie."""
        try:
            # Validation
            if not data.get('washing_quantity_produced') or float(data['washing_quantity_produced']) <= 0:
                QMessageBox.warning(self, "Erreur", "La quantité lavée doit être positive")
                return

            quantity_produced = float(data['washing_quantity_produced'])
            quantity_consumed = float(data.get('washing_quantity_consumed', 0))
            quality_output = data.get('washing_quality_output', '31.0')
            hours = float(data.get('washing_production_hours', 0))

            # Utiliser la date sélectionnée (normalisée sans heure)
            selected_date = self.production_date.date()
            production_datetime = normalize_date(selected_date)

            # Enregistrer la production
            self.db_manager.add_production(
                step=ProcessStepEnum.LAVERIE,
                quantity=quantity_produced,
                quantity_used=quantity_consumed if quantity_consumed > 0 else None,
                production_hours=hours if hours > 0 else None,
                quality=quality_output if quality_output else None,
                production_date=production_datetime
            )

            # Enregistrer les consommations de ressources
            water_used = self.washing_water_input.value()
            reactive_used = self.washing_reactive_input.value()
            energy_used = self.washing_energy_input.value()

            if water_used > 0:
                self.db_manager.add_resource_consumption(
                    resource_type=ResourceType.WATER,
                    quantity=water_used,
                    step=ProcessStepEnum.LAVERIE,
                    consumption_date=production_datetime
                )
                self.washing_water_input.setValue(0)

            if reactive_used > 0:
                self.db_manager.add_resource_consumption(
                    resource_type=ResourceType.REACTIVE,
                    quantity=reactive_used,
                    step=ProcessStepEnum.LAVERIE,
                    consumption_date=production_datetime
                )
                self.washing_reactive_input.setValue(0)

            if energy_used > 0:
                self.db_manager.add_resource_consumption(
                    resource_type=ResourceType.ENERGY,
                    quantity=energy_used,
                    step=ProcessStepEnum.LAVERIE,
                    consumption_date=production_datetime
                )
                self.washing_energy_input.setValue(0)

            # Effacer le formulaire
            self.washing_input.clear_form()

            # Actualiser
            self.update_global_kpi()
            self.check_existing_data()

            QMessageBox.information(self, "✅ Succès",
                f"Production laverie de {quantity_produced:.1f}T enregistrée")

        except ValueError:
            QMessageBox.warning(self, "Erreur", "Veuillez saisir des valeurs numériques valides")
        except Exception as e:
            logging.error(f"Erreur sauvegarde production laverie: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")

    def add_downtime(self):
        """Ajoute un arrêt de production."""
        try:
            unit = self.downtime_unit_combo.currentText()
            reason = self.downtime_reason_combo.currentText().strip()
            duration = self.downtime_duration_input.value()
            description = self.downtime_description_input.toPlainText().strip()

            if not reason:
                QMessageBox.warning(self, "Erreur", "Veuillez saisir une raison pour l'arrêt")
                return

            # Déterminer l'étape
            step = ProcessStepEnum.CONCASSAGE if unit == "Concassage" else ProcessStepEnum.LAVERIE

            # Utiliser la date sélectionnée (normalisée sans heure)
            selected_date = self.production_date.date()
            downtime_datetime = normalize_date(selected_date)

            # Enregistrer l'arrêt
            self.db_manager.add_downtime(
                step=step,
                reason=reason,
                duration=duration,
                description=description if description else None,
                downtime_date=downtime_datetime
            )

            # Effacer le formulaire
            self.downtime_reason_combo.setCurrentIndex(0)
            self.downtime_duration_input.setValue(30)
            self.downtime_description_input.clear()

            # Actualiser
            self.update_global_kpi()

            QMessageBox.information(self, "✅ Succès",
                f"Arrêt de {duration} min enregistré pour {unit}")

        except Exception as e:
            logging.error(f"Erreur ajout arrêt: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")

    # === MÉTHODES DE MISE À JOUR ===

    def update_global_kpi(self):
        """Met à jour les KPI globaux."""
        try:
            # Utiliser la date sélectionnée
            selected_date = self.production_date.date()
            start_datetime, end_datetime = get_date_range_for_day(selected_date)

            # Production concassage du jour sélectionné
            crushing_productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.CONCASSAGE,
                start_date=start_datetime,
                end_date=end_datetime
            )
            crushing_total = sum(p['quantity'] for p in crushing_productions)
            self.crushing_value.setText(f"{crushing_total:.1f} T")

            # Production laverie du jour sélectionné
            washing_productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.LAVERIE,
                start_date=start_datetime,
                end_date=end_datetime
            )
            washing_total = sum(p['quantity'] for p in washing_productions)
            self.washing_value.setText(f"{washing_total:.1f} T")

            # Production totale = production de la laverie (produit final)
            total_production = washing_total
            self.total_production_value.setText(f"{total_production:.1f} T")

            # Interface simplifiée - calculs d'efficacité et qualité supprimés
            # pour réduire l'encombrement de la vue d'ensemble

        except Exception as e:
            logging.error(f"Erreur mise à jour KPI globaux: {str(e)}")

    def refresh_all_data(self):
        """Actualise toutes les données."""
        self.update_global_kpi()

    def refresh_tab(self):
        """Méthode appelée lors du changement d'onglet."""
        self.refresh_all_data()
