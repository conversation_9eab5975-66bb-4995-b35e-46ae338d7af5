"""
Onglet unifié de production regroupant les unités de concassage et laverie
avec leurs consommations et arrêts.
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QMessageBox, QTabWidget,
                            QGroupBox, QLabel, QGridLayout, QFrame, QSplitter,
                            QScrollArea, QWidget, QPushButton, QComboBox, QSpinBox,
                            QDoubleSpinBox, QDateEdit, QTextEdit, QFormLayout)
from PyQt5.QtCore import Qt, QDate, QTimer, pyqtSignal
from PyQt5.QtGui import QFont
from datetime import datetime, timedelta, date
import logging

from .base_optimized import BaseOptimizedTab, QuickInputWidget, StatusWidget
from ui.widgets.professional_widgets import KPILine, StatusIndicator, ActionButton, ProfessionalTable
from models.enums import ProcessStepEnum, StockMovementType, ResourceType
from ui.professional_theme import SotramineTheme
from utils.date_utils import normalize_date, get_date_range_for_day, format_date_for_display

class UnifiedProductionTab(BaseOptimizedTab):
    """Onglet unifié pour la gestion de la production des deux unités."""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "🏭 Production Unifiée", parent)
        self.setup_interface()
        self.setup_timers()
        
    def setup_interface(self):
        """Configure l'interface unifiée."""
        # Layout principal avec splitter horizontal
        main_splitter = QSplitter(Qt.Horizontal)
        self.main_layout.addWidget(main_splitter)
        
        # Zone gauche : Contrôles et saisie
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(10)
        
        # Zone droite : Tableaux de données
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setSpacing(10)
        
        # Ajouter les widgets au splitter
        main_splitter.addWidget(left_widget)
        main_splitter.addWidget(right_widget)
        main_splitter.setSizes([400, 600])  # Proportions initiales
        
        # === ZONE GAUCHE ===
        self.setup_left_panel(left_layout)
        
        # === ZONE DROITE ===
        self.setup_right_panel(right_layout)
        
        # Charger les données initiales
        self.update_kpi_title()
        self.check_existing_data()
        self.load_data_for_date()
        self.refresh_all_data()
        
    def setup_left_panel(self, layout):
        """Configure le panneau gauche avec KPI et saisie."""
        # Section sélection de date
        self.setup_date_selection(layout)

        # Section KPI globale
        self.setup_global_kpi(layout)

        # Onglets pour les unités
        self.setup_unit_tabs(layout)

        # Section arrêts
        self.setup_downtime_section(layout)

    def setup_date_selection(self, layout):
        """Configure la section de sélection de date."""
        date_group = QGroupBox("📅 Date de Production")
        date_group.setFont(QFont("Segoe UI", 10, QFont.Bold))
        date_layout = QHBoxLayout(date_group)

        # Sélecteur de date
        self.production_date = QDateEdit()
        self.production_date.setCalendarPopup(True)
        self.production_date.setDate(QDate.currentDate())
        self.production_date.setDisplayFormat("dddd dd MMMM yyyy")
        self.production_date.dateChanged.connect(self.on_date_changed)
        date_layout.addWidget(QLabel("Date:"))
        date_layout.addWidget(self.production_date)

        # Boutons de navigation rapide
        prev_day_btn = QPushButton("◀ Jour Précédent")
        prev_day_btn.clicked.connect(self.go_to_previous_day)
        date_layout.addWidget(prev_day_btn)

        today_btn = QPushButton("Aujourd'hui")
        today_btn.clicked.connect(self.go_to_today)
        date_layout.addWidget(today_btn)

        next_day_btn = QPushButton("Jour Suivant ▶")
        next_day_btn.clicked.connect(self.go_to_next_day)
        date_layout.addWidget(next_day_btn)

        date_layout.addStretch()

        # Indicateur de données existantes
        self.data_indicator = QLabel("📊 Données disponibles")
        self.data_indicator.setStyleSheet("color: green; font-weight: bold;")
        date_layout.addWidget(self.data_indicator)

        layout.addWidget(date_group)
        
    def setup_global_kpi(self, layout):
        """Configure les KPI globaux."""
        self.kpi_group = QGroupBox("📊 Vue d'ensemble Production")
        self.kpi_group.setFont(QFont("Segoe UI", 10, QFont.Bold))
        kpi_layout = QVBoxLayout(self.kpi_group)
        kpi_layout.setSpacing(5)
        
        # KPI en grille
        kpi_grid = QGridLayout()
        kpi_grid.setSpacing(5)
        
        # Ligne 1 : Production totale (= production laverie)
        self.total_production_line = KPILine("Production Totale (Laverie)", "0", "T")
        kpi_grid.addWidget(self.total_production_line, 0, 0, 1, 2)
        
        # Ligne 2 : Concassage et Laverie
        self.crushing_production_line = KPILine("Concassage (Intermédiaire)", "0", "T")
        self.washing_production_line = KPILine("Laverie (Final)", "0", "T")
        kpi_grid.addWidget(self.crushing_production_line, 1, 0)
        kpi_grid.addWidget(self.washing_production_line, 1, 1)
        
        # Ligne 3 : Efficacité et Qualité
        self.global_efficiency_line = KPILine("Efficacité Globale", "0", "%")
        self.average_quality_line = KPILine("Qualité Finale", "0", "%")
        kpi_grid.addWidget(self.global_efficiency_line, 2, 0)
        kpi_grid.addWidget(self.average_quality_line, 2, 1)

        # Ligne 4 : Taux de transformation
        self.transformation_rate_line = KPILine("Taux Transformation", "0", "%")
        kpi_grid.addWidget(self.transformation_rate_line, 3, 0, 1, 2)
        
        kpi_layout.addLayout(kpi_grid)
        layout.addWidget(self.kpi_group)
        
    def setup_unit_tabs(self, layout):
        """Configure les onglets pour chaque unité."""
        units_group = QGroupBox("🔧 Saisie par Unité")
        units_group.setFont(QFont("Segoe UI", 10, QFont.Bold))
        units_layout = QVBoxLayout(units_group)
        
        # Onglets pour les unités
        self.unit_tabs = QTabWidget()
        
        # Onglet Concassage
        crushing_widget = self.create_crushing_tab()
        self.unit_tabs.addTab(crushing_widget, "🔨 Concassage")
        
        # Onglet Laverie
        washing_widget = self.create_washing_tab()
        self.unit_tabs.addTab(washing_widget, "💧 Laverie")
        
        units_layout.addWidget(self.unit_tabs)
        layout.addWidget(units_group)
        
    def create_crushing_tab(self):
        """Crée l'onglet de saisie pour le concassage."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(8)
        
        # Formulaire de saisie concassage
        fields = [
            {
                'name': 'crushing_quantity_produced',
                'label': 'Quantité Produite',
                'type': 'double',
                'suffix': 'T'
            },
            {
                'name': 'crushing_quantity_consumed',
                'label': 'Quantité Consommée',
                'type': 'double',
                'suffix': 'T'
            },
            {
                'name': 'crushing_production_hours',
                'label': 'Heures de Production',
                'type': 'double',
                'suffix': 'h'
            },
            {
                'name': 'crushing_quality',
                'label': 'Qualité Sortie',
                'type': 'double',
                'suffix': '%'
            }
        ]
        
        self.crushing_input = QuickInputWidget(fields)
        self.crushing_input.data_saved.connect(self.save_crushing_production)
        layout.addWidget(self.crushing_input)
        
        # Section consommations concassage
        consumption_group = QGroupBox("Consommations")
        consumption_layout = QFormLayout(consumption_group)
        
        self.crushing_energy_input = QDoubleSpinBox()
        self.crushing_energy_input.setRange(0, 99999)
        self.crushing_energy_input.setSuffix(" kWh")
        consumption_layout.addRow("Énergie:", self.crushing_energy_input)
        
        layout.addWidget(consumption_group)
        layout.addStretch()
        
        return widget
        
    def create_washing_tab(self):
        """Crée l'onglet de saisie pour la laverie."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(8)
        
        # Formulaire de saisie laverie
        fields = [
            {
                'name': 'washing_quantity_produced',
                'label': 'Quantité Lavée',
                'type': 'double',
                'suffix': 'T'
            },
            {
                'name': 'washing_quantity_consumed',
                'label': 'Quantité Consommée',
                'type': 'double',
                'suffix': 'T'
            },
            {
                'name': 'washing_quality_input',
                'label': 'Qualité Entrée',
                'type': 'double',
                'suffix': '%'
            },
            {
                'name': 'washing_quality_output',
                'label': 'Qualité Sortie',
                'type': 'double',
                'suffix': '%'
            },
            {
                'name': 'washing_production_hours',
                'label': 'Heures de Production',
                'type': 'double',
                'suffix': 'h'
            }
        ]
        
        self.washing_input = QuickInputWidget(fields)
        self.washing_input.data_saved.connect(self.save_washing_production)
        layout.addWidget(self.washing_input)
        
        # Section consommations laverie
        consumption_group = QGroupBox("Consommations")
        consumption_layout = QFormLayout(consumption_group)
        
        self.washing_water_input = QDoubleSpinBox()
        self.washing_water_input.setRange(0, 99999)
        self.washing_water_input.setSuffix(" m³")
        consumption_layout.addRow("Eau:", self.washing_water_input)
        
        self.washing_reactive_input = QDoubleSpinBox()
        self.washing_reactive_input.setRange(0, 99999)
        self.washing_reactive_input.setSuffix(" kg")
        consumption_layout.addRow("Réactifs:", self.washing_reactive_input)
        
        self.washing_energy_input = QDoubleSpinBox()
        self.washing_energy_input.setRange(0, 99999)
        self.washing_energy_input.setSuffix(" kWh")
        consumption_layout.addRow("Énergie:", self.washing_energy_input)
        
        layout.addWidget(consumption_group)
        layout.addStretch()
        
        return widget

    def setup_downtime_section(self, layout):
        """Configure la section de gestion des arrêts."""
        downtime_group = QGroupBox("⏹️ Gestion des Arrêts")
        downtime_group.setFont(QFont("Segoe UI", 10, QFont.Bold))
        downtime_layout = QVBoxLayout(downtime_group)

        # Formulaire d'ajout d'arrêt
        form_layout = QFormLayout()

        # Sélection de l'unité
        self.downtime_unit_combo = QComboBox()
        self.downtime_unit_combo.addItems(["Concassage", "Laverie"])
        form_layout.addRow("Unité:", self.downtime_unit_combo)

        # Raison de l'arrêt
        self.downtime_reason_combo = QComboBox()
        self.downtime_reason_combo.setEditable(True)
        self.downtime_reason_combo.addItems([
            "Maintenance préventive",
            "Panne équipement",
            "Problème qualité",
            "Manque matière première",
            "Changement d'équipe",
            "Nettoyage",
            "Autre"
        ])
        form_layout.addRow("Raison:", self.downtime_reason_combo)

        # Durée de l'arrêt
        self.downtime_duration_input = QSpinBox()
        self.downtime_duration_input.setRange(1, 9999)
        self.downtime_duration_input.setSuffix(" min")
        self.downtime_duration_input.setValue(30)
        form_layout.addRow("Durée:", self.downtime_duration_input)

        # Description
        self.downtime_description_input = QTextEdit()
        self.downtime_description_input.setMaximumHeight(60)
        self.downtime_description_input.setPlaceholderText("Description détaillée (optionnel)")
        form_layout.addRow("Description:", self.downtime_description_input)

        # Bouton d'ajout
        add_downtime_btn = QPushButton("Ajouter Arrêt")
        add_downtime_btn.clicked.connect(self.add_downtime)
        form_layout.addRow("", add_downtime_btn)

        downtime_layout.addLayout(form_layout)
        layout.addWidget(downtime_group)

    def setup_right_panel(self, layout):
        """Configure le panneau droit avec les tableaux."""
        # Onglets pour les différents tableaux
        self.data_tabs = QTabWidget()

        # Tableau de production
        self.setup_production_table()
        self.data_tabs.addTab(self.production_table_widget, "📈 Production")

        # Tableau des consommations
        self.setup_consumption_table()
        self.data_tabs.addTab(self.consumption_table_widget, "⚡ Consommations")

        # Tableau des arrêts
        self.setup_downtime_table()
        self.data_tabs.addTab(self.downtime_table_widget, "⏹️ Arrêts")

        layout.addWidget(self.data_tabs)

    def setup_production_table(self):
        """Configure le tableau de production."""
        self.production_table_widget = QWidget()
        layout = QVBoxLayout(self.production_table_widget)

        # En-tête avec filtres
        header_layout = QHBoxLayout()
        header_layout.addWidget(QLabel("<b>Historique de Production</b>"))

        # Filtre par unité
        self.production_unit_filter = QComboBox()
        self.production_unit_filter.addItems(["Toutes les unités", "Concassage", "Laverie"])
        self.production_unit_filter.currentTextChanged.connect(self.filter_production_data)
        header_layout.addWidget(QLabel("Unité:"))
        header_layout.addWidget(self.production_unit_filter)
        header_layout.addStretch()

        layout.addLayout(header_layout)

        # Tableau
        headers = ["Date/Heure", "Unité", "Produit (T)", "Consommé (T)",
                  "Heures", "Qualité (%)", "Rendement (T/h)", "Efficacité (%)"]
        self.production_table = ProfessionalTable()
        self.production_table.setColumnCount(len(headers))
        self.production_table.setHorizontalHeaderLabels(headers)
        layout.addWidget(self.production_table)

    def setup_consumption_table(self):
        """Configure le tableau des consommations."""
        self.consumption_table_widget = QWidget()
        layout = QVBoxLayout(self.consumption_table_widget)

        # En-tête
        layout.addWidget(QLabel("<b>Historique des Consommations</b>"))

        # Tableau
        headers = ["Date/Heure", "Unité", "Ressource", "Quantité", "Unité"]
        self.consumption_table = ProfessionalTable()
        self.consumption_table.setColumnCount(len(headers))
        self.consumption_table.setHorizontalHeaderLabels(headers)
        layout.addWidget(self.consumption_table)

    def setup_downtime_table(self):
        """Configure le tableau des arrêts."""
        self.downtime_table_widget = QWidget()
        layout = QVBoxLayout(self.downtime_table_widget)

        # En-tête
        layout.addWidget(QLabel("<b>Historique des Arrêts</b>"))

        # Tableau
        headers = ["Date/Heure", "Unité", "Raison", "Durée (min)", "Description"]
        self.downtime_table = ProfessionalTable()
        self.downtime_table.setColumnCount(len(headers))
        self.downtime_table.setHorizontalHeaderLabels(headers)
        layout.addWidget(self.downtime_table)

    def setup_timers(self):
        """Configure les timers pour les mises à jour automatiques."""
        # Timer pour les KPI
        self.kpi_timer = QTimer()
        self.kpi_timer.timeout.connect(self.update_global_kpi)
        self.kpi_timer.start(30000)  # 30 secondes

        # Timer pour les tableaux
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self.refresh_tables)
        self.data_timer.start(60000)  # 1 minute

    # === MÉTHODES DE NAVIGATION DE DATE ===

    def on_date_changed(self):
        """Appelée quand la date change."""
        self.update_kpi_title()
        self.check_existing_data()
        self.load_data_for_date()
        self.update_global_kpi()

    def update_kpi_title(self):
        """Met à jour le titre des KPI avec la date sélectionnée."""
        selected_date = self.production_date.date()
        formatted_date = format_date_for_display(selected_date.toPyDate())
        self.kpi_group.setTitle(f"📊 Vue d'ensemble Production - {formatted_date}")

    def go_to_previous_day(self):
        """Va au jour précédent."""
        current_date = self.production_date.date()
        previous_date = current_date.addDays(-1)
        self.production_date.setDate(previous_date)

    def go_to_next_day(self):
        """Va au jour suivant."""
        current_date = self.production_date.date()
        next_date = current_date.addDays(1)
        # Ne pas aller au-delà d'aujourd'hui
        if next_date <= QDate.currentDate():
            self.production_date.setDate(next_date)

    def go_to_today(self):
        """Va à aujourd'hui."""
        self.production_date.setDate(QDate.currentDate())

    def check_existing_data(self):
        """Vérifie si des données existent pour la date sélectionnée."""
        selected_date = self.production_date.date()
        start_datetime, end_datetime = get_date_range_for_day(selected_date)

        # Vérifier les productions
        crushing_productions = self.db_manager.get_production_summary(
            step=ProcessStepEnum.CONCASSAGE,
            start_date=start_datetime,
            end_date=end_datetime
        )

        washing_productions = self.db_manager.get_production_summary(
            step=ProcessStepEnum.LAVERIE,
            start_date=start_datetime,
            end_date=end_datetime
        )

        has_data = len(crushing_productions) > 0 or len(washing_productions) > 0

        if has_data:
            self.data_indicator.setText("📊 Données disponibles")
            self.data_indicator.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.data_indicator.setText("📝 Aucune donnée")
            self.data_indicator.setStyleSheet("color: orange; font-weight: bold;")

    def load_data_for_date(self):
        """Charge les données existantes pour la date sélectionnée."""
        selected_date = self.production_date.date()
        start_datetime, end_datetime = get_date_range_for_day(selected_date)

        # Charger les productions du concassage
        crushing_productions = self.db_manager.get_production_summary(
            step=ProcessStepEnum.CONCASSAGE,
            start_date=start_datetime,
            end_date=end_datetime
        )

        # Charger les productions de la laverie
        washing_productions = self.db_manager.get_production_summary(
            step=ProcessStepEnum.LAVERIE,
            start_date=start_datetime,
            end_date=end_datetime
        )

        # Pré-remplir les formulaires si des données existent
        if crushing_productions:
            latest_crushing = crushing_productions[-1]  # Dernière production
            self.prefill_crushing_form(latest_crushing)

        if washing_productions:
            latest_washing = washing_productions[-1]  # Dernière production
            self.prefill_washing_form(latest_washing)

    def prefill_crushing_form(self, production_data):
        """Pré-remplit le formulaire de concassage avec les données existantes."""
        try:
            # Pré-remplir les champs du formulaire
            if 'quantity' in production_data:
                self.crushing_input.fields['crushing_quantity_produced'].setValue(production_data['quantity'])
            if 'quantity_used' in production_data and production_data['quantity_used']:
                self.crushing_input.fields['crushing_quantity_consumed'].setValue(production_data['quantity_used'])
            if 'production_hours' in production_data and production_data['production_hours']:
                self.crushing_input.fields['crushing_production_hours'].setValue(production_data['production_hours'])
            if 'quality' in production_data and production_data['quality']:
                quality_str = str(production_data['quality']).replace('%', '').strip()
                try:
                    quality_val = float(quality_str)
                    self.crushing_input.fields['crushing_quality'].setValue(quality_val)
                except (ValueError, TypeError):
                    pass
        except Exception as e:
            logging.error(f"Erreur pré-remplissage concassage: {str(e)}")

    def prefill_washing_form(self, production_data):
        """Pré-remplit le formulaire de laverie avec les données existantes."""
        try:
            # Pré-remplir les champs du formulaire
            if 'quantity' in production_data:
                self.washing_input.fields['washing_quantity_produced'].setValue(production_data['quantity'])
            if 'quantity_used' in production_data and production_data['quantity_used']:
                self.washing_input.fields['washing_quantity_consumed'].setValue(production_data['quantity_used'])
            if 'production_hours' in production_data and production_data['production_hours']:
                self.washing_input.fields['washing_production_hours'].setValue(production_data['production_hours'])
            if 'quality' in production_data and production_data['quality']:
                quality_str = str(production_data['quality']).replace('%', '').strip()
                try:
                    quality_val = float(quality_str)
                    self.washing_input.fields['washing_quality_output'].setValue(quality_val)
                except (ValueError, TypeError):
                    pass
        except Exception as e:
            logging.error(f"Erreur pré-remplissage laverie: {str(e)}")

    # === MÉTHODES DE SAUVEGARDE ===

    def save_crushing_production(self, data):
        """Sauvegarde une production de concassage."""
        try:
            # Validation
            if not data.get('crushing_quantity_produced') or float(data['crushing_quantity_produced']) <= 0:
                QMessageBox.warning(self, "Erreur", "La quantité produite doit être positive")
                return

            quantity_produced = float(data['crushing_quantity_produced'])
            quantity_consumed = float(data.get('crushing_quantity_consumed', 0))
            hours = float(data.get('crushing_production_hours', 0))
            quality_str = data.get('crushing_quality', '30.0')

            # Convertir la qualité
            if quality_str and isinstance(quality_str, str):
                quality_str = quality_str.replace('%', '').strip()
            quality_value = float(quality_str) if quality_str else None

            # Utiliser la date sélectionnée (normalisée sans heure)
            selected_date = self.production_date.date()
            production_datetime = normalize_date(selected_date)

            # Enregistrer la production
            self.db_manager.add_production(
                step=ProcessStepEnum.CONCASSAGE,
                quantity=quantity_produced,
                quantity_used=quantity_consumed if quantity_consumed > 0 else None,
                production_hours=hours if hours > 0 else None,
                quality=f"{quality_value}%" if quality_value is not None else None,
                production_date=production_datetime
            )

            # Enregistrer la consommation d'énergie si saisie
            energy_consumption = self.crushing_energy_input.value()
            if energy_consumption > 0:
                self.db_manager.add_resource_consumption(
                    resource_type=ResourceType.ENERGY,
                    quantity=energy_consumption,
                    step=ProcessStepEnum.CONCASSAGE,
                    consumption_date=production_datetime
                )
                self.crushing_energy_input.setValue(0)

            # Effacer le formulaire
            self.crushing_input.clear_form()

            # Actualiser
            self.update_global_kpi()
            self.refresh_tables()

            QMessageBox.information(self, "✅ Succès",
                f"Production concassage de {quantity_produced:.1f}T enregistrée")

        except ValueError as e:
            error_msg = str(e)
            if "Stock insuffisant" in error_msg:
                QMessageBox.warning(self, "Stock Insuffisant", error_msg)
            else:
                QMessageBox.warning(self, "Erreur", "Veuillez saisir des valeurs numériques valides")
        except Exception as e:
            logging.error(f"Erreur sauvegarde production concassage: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")

    def save_washing_production(self, data):
        """Sauvegarde une production de laverie."""
        try:
            # Validation
            if not data.get('washing_quantity_produced') or float(data['washing_quantity_produced']) <= 0:
                QMessageBox.warning(self, "Erreur", "La quantité lavée doit être positive")
                return

            quantity_produced = float(data['washing_quantity_produced'])
            quantity_consumed = float(data.get('washing_quantity_consumed', 0))
            quality_output = data.get('washing_quality_output', '31.0')
            hours = float(data.get('washing_production_hours', 0))

            # Utiliser la date sélectionnée (normalisée sans heure)
            selected_date = self.production_date.date()
            production_datetime = normalize_date(selected_date)

            # Enregistrer la production
            self.db_manager.add_production(
                step=ProcessStepEnum.LAVERIE,
                quantity=quantity_produced,
                quantity_used=quantity_consumed if quantity_consumed > 0 else None,
                production_hours=hours if hours > 0 else None,
                quality=quality_output if quality_output else None,
                production_date=production_datetime
            )

            # Enregistrer les consommations de ressources
            water_used = self.washing_water_input.value()
            reactive_used = self.washing_reactive_input.value()
            energy_used = self.washing_energy_input.value()

            if water_used > 0:
                self.db_manager.add_resource_consumption(
                    resource_type=ResourceType.WATER,
                    quantity=water_used,
                    step=ProcessStepEnum.LAVERIE,
                    consumption_date=production_datetime
                )
                self.washing_water_input.setValue(0)

            if reactive_used > 0:
                self.db_manager.add_resource_consumption(
                    resource_type=ResourceType.REACTIVE,
                    quantity=reactive_used,
                    step=ProcessStepEnum.LAVERIE,
                    consumption_date=production_datetime
                )
                self.washing_reactive_input.setValue(0)

            if energy_used > 0:
                self.db_manager.add_resource_consumption(
                    resource_type=ResourceType.ENERGY,
                    quantity=energy_used,
                    step=ProcessStepEnum.LAVERIE,
                    consumption_date=production_datetime
                )
                self.washing_energy_input.setValue(0)

            # Effacer le formulaire
            self.washing_input.clear_form()

            # Actualiser
            self.update_global_kpi()
            self.refresh_tables()

            QMessageBox.information(self, "✅ Succès",
                f"Production laverie de {quantity_produced:.1f}T enregistrée")

        except ValueError:
            QMessageBox.warning(self, "Erreur", "Veuillez saisir des valeurs numériques valides")
        except Exception as e:
            logging.error(f"Erreur sauvegarde production laverie: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")

    def add_downtime(self):
        """Ajoute un arrêt de production."""
        try:
            unit = self.downtime_unit_combo.currentText()
            reason = self.downtime_reason_combo.currentText().strip()
            duration = self.downtime_duration_input.value()
            description = self.downtime_description_input.toPlainText().strip()

            if not reason:
                QMessageBox.warning(self, "Erreur", "Veuillez saisir une raison pour l'arrêt")
                return

            # Déterminer l'étape
            step = ProcessStepEnum.CONCASSAGE if unit == "Concassage" else ProcessStepEnum.LAVERIE

            # Utiliser la date sélectionnée (normalisée sans heure)
            selected_date = self.production_date.date()
            downtime_datetime = normalize_date(selected_date)

            # Enregistrer l'arrêt
            self.db_manager.add_downtime(
                step=step,
                reason=reason,
                duration=duration,
                description=description if description else None,
                downtime_date=downtime_datetime
            )

            # Effacer le formulaire
            self.downtime_reason_combo.setCurrentIndex(0)
            self.downtime_duration_input.setValue(30)
            self.downtime_description_input.clear()

            # Actualiser
            self.refresh_tables()

            QMessageBox.information(self, "✅ Succès",
                f"Arrêt de {duration} min enregistré pour {unit}")

        except Exception as e:
            logging.error(f"Erreur ajout arrêt: {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {str(e)}")

    # === MÉTHODES DE MISE À JOUR ===

    def update_global_kpi(self):
        """Met à jour les KPI globaux."""
        try:
            # Utiliser la date sélectionnée
            selected_date = self.production_date.date()
            start_datetime, end_datetime = get_date_range_for_day(selected_date)

            # Production concassage du jour sélectionné
            crushing_productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.CONCASSAGE,
                start_date=start_datetime,
                end_date=end_datetime
            )
            crushing_total = sum(p['quantity'] for p in crushing_productions)
            self.crushing_production_line.update_value(f"{crushing_total:.1f}")

            # Production laverie du jour sélectionné
            washing_productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.LAVERIE,
                start_date=start_datetime,
                end_date=end_datetime
            )
            washing_total = sum(p['quantity'] for p in washing_productions)
            self.washing_production_line.update_value(f"{washing_total:.1f}")

            # Production totale = production de la laverie (produit final)
            total_production = washing_total
            self.total_production_line.update_value(f"{total_production:.1f}")

            # Efficacité globale (production finale vs matière première consommée)
            # Calculer la consommation totale de matière première (entrée du concassage)
            crushing_consumed = sum(p.get('quantity_used', 0) or 0 for p in crushing_productions)
            if crushing_consumed > 0:
                # Efficacité = production finale / matière première consommée
                global_efficiency = (total_production / crushing_consumed) * 100
                self.global_efficiency_line.update_value(f"{global_efficiency:.1f}")
            else:
                self.global_efficiency_line.update_value("0")

            # Taux de transformation (concassage vers laverie)
            if crushing_total > 0:
                transformation_rate = (washing_total / crushing_total) * 100
                self.transformation_rate_line.update_value(f"{transformation_rate:.1f}")
            else:
                self.transformation_rate_line.update_value("0")

            # Qualité finale (moyenne pondérée de la laverie uniquement)
            total_weighted_quality = 0
            total_quantity_with_quality = 0

            # Utiliser seulement les productions de laverie pour la qualité finale
            for p in washing_productions:
                if p.get('quality') and p['quantity'] > 0:
                    try:
                        if isinstance(p['quality'], str) and '%' in p['quality']:
                            quality_val = float(p['quality'].replace('%', '').strip())
                        else:
                            quality_val = float(p['quality'])
                        total_weighted_quality += quality_val * p['quantity']
                        total_quantity_with_quality += p['quantity']
                    except (ValueError, TypeError):
                        pass

            if total_quantity_with_quality > 0:
                avg_quality = total_weighted_quality / total_quantity_with_quality
                self.average_quality_line.update_value(f"{avg_quality:.1f}")
            else:
                self.average_quality_line.update_value("0")

        except Exception as e:
            logging.error(f"Erreur mise à jour KPI globaux: {str(e)}")

    def refresh_all_data(self):
        """Actualise toutes les données."""
        self.update_global_kpi()
        self.refresh_tables()

    def refresh_tables(self):
        """Actualise tous les tableaux."""
        self.refresh_production_table()
        self.refresh_consumption_table()
        self.refresh_downtime_table()

    def refresh_production_table(self):
        """Actualise le tableau de production."""
        try:
            # Récupérer les données des 7 derniers jours
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)

            # Récupérer les productions des deux unités
            crushing_productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.CONCASSAGE,
                start_date=start_date,
                end_date=end_date
            )

            washing_productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.LAVERIE,
                start_date=start_date,
                end_date=end_date
            )

            # Combiner et trier par date
            all_productions = []
            for p in crushing_productions:
                p['unit'] = 'Concassage'
                all_productions.append(p)
            for p in washing_productions:
                p['unit'] = 'Laverie'
                all_productions.append(p)

            all_productions.sort(key=lambda x: x['production_date'], reverse=True)

            # Filtrer selon le filtre sélectionné
            unit_filter = self.production_unit_filter.currentText()
            if unit_filter != "Toutes les unités":
                all_productions = [p for p in all_productions if p['unit'] == unit_filter]

            self.populate_production_table(all_productions)

        except Exception as e:
            logging.error(f"Erreur actualisation tableau production: {str(e)}")

    def populate_production_table(self, productions):
        """Remplit le tableau de production."""
        self.production_table.setRowCount(len(productions))

        for row, production in enumerate(productions):
            # Date/Heure
            self.production_table.setItem(row, 0,
                self.production_table.itemClass()(production['production_date'].strftime("%d/%m/%Y %H:%M")))

            # Unité
            self.production_table.setItem(row, 1,
                self.production_table.itemClass()(production['unit']))

            # Quantité produite
            self.production_table.setItem(row, 2,
                self.production_table.itemClass()(f"{production['quantity']:.2f}"))

            # Quantité consommée
            consumed = production.get('quantity_used', 0)
            self.production_table.setItem(row, 3,
                self.production_table.itemClass()(f"{consumed:.2f}" if consumed else "-"))

            # Heures
            hours = production.get('production_hours', 0)
            self.production_table.setItem(row, 4,
                self.production_table.itemClass()(f"{hours:.2f}" if hours else "-"))

            # Qualité
            quality = production.get('quality', '')
            if quality:
                if isinstance(quality, (int, float)):
                    quality_display = f"{quality:.1f}%"
                elif isinstance(quality, str) and '%' not in quality:
                    try:
                        quality_float = float(quality)
                        quality_display = f"{quality_float:.1f}%"
                    except (ValueError, TypeError):
                        quality_display = quality
                else:
                    quality_display = quality
            else:
                quality_display = "-"
            self.production_table.setItem(row, 5,
                self.production_table.itemClass()(quality_display))

            # Rendement
            if hours and hours > 0:
                rendement = production['quantity'] / hours
                self.production_table.setItem(row, 6,
                    self.production_table.itemClass()(f"{rendement:.2f}"))
            else:
                self.production_table.setItem(row, 6,
                    self.production_table.itemClass()("-"))

            # Efficacité
            if consumed and consumed > 0:
                efficiency = (production['quantity'] / consumed) * 100
                self.production_table.setItem(row, 7,
                    self.production_table.itemClass()(f"{efficiency:.1f}%"))
            else:
                self.production_table.setItem(row, 7,
                    self.production_table.itemClass()("-"))

    def refresh_consumption_table(self):
        """Actualise le tableau des consommations."""
        try:
            # Récupérer les consommations des 7 derniers jours
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)

            consumptions = self.db_manager.get_resource_consumptions(
                start_date=start_date,
                end_date=end_date
            )

            self.populate_consumption_table(consumptions)

        except Exception as e:
            logging.error(f"Erreur actualisation tableau consommations: {str(e)}")

    def populate_consumption_table(self, consumptions):
        """Remplit le tableau des consommations."""
        self.consumption_table.setRowCount(len(consumptions))

        # Mapping des unités pour l'affichage
        unit_map = {
            ProcessStepEnum.CONCASSAGE.value: "Concassage",
            ProcessStepEnum.LAVERIE.value: "Laverie"
        }

        # Mapping des unités pour les ressources
        unit_display_map = {
            ResourceType.WATER.value: "m³",
            ResourceType.ENERGY.value: "kWh",
            ResourceType.REACTIVE.value: "kg"
        }

        for row, consumption in enumerate(consumptions):
            # Date/Heure
            self.consumption_table.setItem(row, 0,
                self.consumption_table.itemClass()(consumption['consumption_date'].strftime("%d/%m/%Y %H:%M")))

            # Unité (peut être None pour les consommations globales)
            unit_name = "Global"
            if consumption.get('step_name'):
                unit_name = unit_map.get(consumption['step_name'], consumption['step_name'])
            self.consumption_table.setItem(row, 1,
                self.consumption_table.itemClass()(unit_name))

            # Ressource
            self.consumption_table.setItem(row, 2,
                self.consumption_table.itemClass()(consumption['resource_type']))

            # Quantité
            self.consumption_table.setItem(row, 3,
                self.consumption_table.itemClass()(f"{consumption['quantity']:.2f}"))

            # Unité
            unit_display = unit_display_map.get(consumption['resource_type'], "")
            self.consumption_table.setItem(row, 4,
                self.consumption_table.itemClass()(unit_display))

    def refresh_downtime_table(self):
        """Actualise le tableau des arrêts."""
        try:
            # Récupérer les arrêts des 7 derniers jours
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)

            # Récupérer les arrêts des deux unités
            crushing_downtimes = self.db_manager.get_downtime_summary(
                step=ProcessStepEnum.CONCASSAGE,
                start_date=start_date,
                end_date=end_date
            )

            washing_downtimes = self.db_manager.get_downtime_summary(
                step=ProcessStepEnum.LAVERIE,
                start_date=start_date,
                end_date=end_date
            )

            # Combiner et trier
            all_downtimes = []
            for d in crushing_downtimes:
                d['unit'] = 'Concassage'
                all_downtimes.append(d)
            for d in washing_downtimes:
                d['unit'] = 'Laverie'
                all_downtimes.append(d)

            all_downtimes.sort(key=lambda x: x['downtime_date'], reverse=True)

            self.populate_downtime_table(all_downtimes)

        except Exception as e:
            logging.error(f"Erreur actualisation tableau arrêts: {str(e)}")

    def populate_downtime_table(self, downtimes):
        """Remplit le tableau des arrêts."""
        self.downtime_table.setRowCount(len(downtimes))

        for row, downtime in enumerate(downtimes):
            # Date/Heure
            self.downtime_table.setItem(row, 0,
                self.downtime_table.itemClass()(downtime['downtime_date'].strftime("%d/%m/%Y %H:%M")))

            # Unité
            self.downtime_table.setItem(row, 1,
                self.downtime_table.itemClass()(downtime['unit']))

            # Raison
            self.downtime_table.setItem(row, 2,
                self.downtime_table.itemClass()(downtime['reason']))

            # Durée
            self.downtime_table.setItem(row, 3,
                self.downtime_table.itemClass()(f"{downtime['duration']:.0f}"))

            # Description
            description = downtime.get('description', '') or ''
            self.downtime_table.setItem(row, 4,
                self.downtime_table.itemClass()(description))

    def filter_production_data(self):
        """Filtre les données de production selon l'unité sélectionnée."""
        self.refresh_production_table()

    def refresh_tab(self):
        """Méthode appelée lors du changement d'onglet."""
        self.refresh_all_data()
