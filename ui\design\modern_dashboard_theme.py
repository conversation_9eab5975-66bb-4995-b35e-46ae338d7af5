"""
Thème de dashboard moderne inspiré du design financier pour l'application SOTRAMINE.
Design basé sur les principes de dashboard avec cartes, métriques et visualisations.
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QFrame
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QColor, QPainter, QBrush, QPen
import math

class ModernCard(QFrame):
    """Carte moderne avec ombre et coins arrondis."""
    
    def __init__(self, title="", value="", unit="", trend=None, color="#2E86AB"):
        super().__init__()
        self.title = title
        self.value = value
        self.unit = unit
        self.trend = trend
        self.color = color
        self.setup_ui()
        
    def setup_ui(self):
        """Configure l'interface de la carte."""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet(f"""
            ModernCard {{
                background-color: white;
                border: 1px solid #E5E7EB;
                border-radius: 12px;
                padding: 16px;
                margin: 4px;
            }}
            ModernCard:hover {{
                border: 1px solid {self.color};
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        layout.setContentsMargins(16, 16, 16, 16)
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setStyleSheet("""
            color: #6B7280;
            font-size: 14px;
            font-weight: 500;
        """)
        layout.addWidget(title_label)
        
        # Valeur principale
        value_layout = QHBoxLayout()
        value_label = QLabel(self.value)
        value_label.setStyleSheet(f"""
            color: #111827;
            font-size: 28px;
            font-weight: 700;
        """)
        value_layout.addWidget(value_label)
        
        if self.unit:
            unit_label = QLabel(self.unit)
            unit_label.setStyleSheet("""
                color: #6B7280;
                font-size: 16px;
                font-weight: 500;
                margin-left: 4px;
            """)
            value_layout.addWidget(unit_label)
            
        value_layout.addStretch()
        layout.addLayout(value_layout)
        
        # Tendance (optionnel)
        if self.trend:
            trend_label = QLabel(self.trend)
            trend_color = "#10B981" if "+" in self.trend else "#EF4444"
            trend_label.setStyleSheet(f"""
                color: {trend_color};
                font-size: 12px;
                font-weight: 600;
            """)
            layout.addWidget(trend_label)

class ProductionOverviewWidget(QWidget):
    """Widget d'aperçu de production inspiré du dashboard financier."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Configure l'interface d'aperçu."""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # En-tête
        header_layout = QHBoxLayout()
        
        title = QLabel("📊 Vue d'ensemble Production")
        title.setStyleSheet("""
            color: #111827;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        """)
        header_layout.addWidget(title)
        header_layout.addStretch()
        
        # Date actuelle
        date_label = QLabel("Aujourd'hui")
        date_label.setStyleSheet("""
            color: #6B7280;
            font-size: 14px;
            background-color: #F3F4F6;
            padding: 6px 12px;
            border-radius: 6px;
        """)
        header_layout.addWidget(date_label)
        
        layout.addLayout(header_layout)
        
        # Cartes principales (inspirées de current_balance, income, expenses)
        main_cards_layout = QGridLayout()
        main_cards_layout.setSpacing(16)
        
        # Production totale (équivalent current_balance)
        self.total_production_card = ModernCard(
            "Production Totale Jour",
            "0.0",
            "T",
            "+2.5% vs hier",
            "#2E86AB"
        )
        main_cards_layout.addWidget(self.total_production_card, 0, 0)
        
        # Efficacité (équivalent income)
        self.efficiency_card = ModernCard(
            "Efficacité Globale",
            "0.0",
            "%",
            "+1.2% vs hier",
            "#10B981"
        )
        main_cards_layout.addWidget(self.efficiency_card, 0, 1)
        
        # Temps d'arrêt (équivalent expenses)
        self.downtime_card = ModernCard(
            "Temps d'Arrêt",
            "0",
            "min",
            "-15 min vs hier",
            "#EF4444"
        )
        main_cards_layout.addWidget(self.downtime_card, 0, 2)
        
        layout.addLayout(main_cards_layout)
        
        # Section unités (inspirée de pots)
        self.setup_units_section(layout)
        
        # Section activité récente (inspirée de transactions)
        self.setup_recent_activity_section(layout)
        
    def setup_units_section(self, layout):
        """Configure la section des unités de production."""
        units_title = QLabel("🏭 Unités de Production")
        units_title.setStyleSheet("""
            color: #111827;
            font-size: 18px;
            font-weight: 600;
            margin-top: 16px;
            margin-bottom: 12px;
        """)
        layout.addWidget(units_title)
        
        units_layout = QHBoxLayout()
        units_layout.setSpacing(16)
        
        # Concassage
        self.crushing_card = ModernCard(
            "Concassage",
            "0.0",
            "T",
            "8h actif",
            "#F59E0B"
        )
        units_layout.addWidget(self.crushing_card)
        
        # Laverie
        self.washing_card = ModernCard(
            "Laverie",
            "0.0",
            "T",
            "7.5h actif",
            "#3B82F6"
        )
        units_layout.addWidget(self.washing_card)
        
        # Qualité
        self.quality_card = ModernCard(
            "Qualité Moyenne",
            "0.0",
            "% P2O5",
            "Conforme",
            "#8B5CF6"
        )
        units_layout.addWidget(self.quality_card)
        
        layout.addLayout(units_layout)
        
    def setup_recent_activity_section(self, layout):
        """Configure la section d'activité récente."""
        activity_title = QLabel("📋 Activité Récente")
        activity_title.setStyleSheet("""
            color: #111827;
            font-size: 18px;
            font-weight: 600;
            margin-top: 16px;
            margin-bottom: 12px;
        """)
        layout.addWidget(activity_title)
        
        # Container pour les activités
        activity_container = QFrame()
        activity_container.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #E5E7EB;
                border-radius: 12px;
                padding: 16px;
            }
        """)
        
        activity_layout = QVBoxLayout(activity_container)
        activity_layout.setSpacing(12)
        
        # Exemples d'activités récentes
        activities = [
            {"time": "14:30", "action": "Production laverie", "value": "+125.5 T", "color": "#10B981"},
            {"time": "13:45", "action": "Arrêt maintenance", "value": "30 min", "color": "#EF4444"},
            {"time": "12:15", "action": "Production concassage", "value": "+200.0 T", "color": "#F59E0B"},
            {"time": "11:30", "action": "Consommation eau", "value": "45 m³", "color": "#3B82F6"},
        ]
        
        for activity in activities:
            activity_item = self.create_activity_item(
                activity["time"], 
                activity["action"], 
                activity["value"], 
                activity["color"]
            )
            activity_layout.addWidget(activity_item)
            
        layout.addWidget(activity_container)
        
    def create_activity_item(self, time, action, value, color):
        """Crée un élément d'activité."""
        item = QFrame()
        item.setStyleSheet("""
            QFrame {
                background-color: #F9FAFB;
                border-radius: 8px;
                padding: 12px;
            }
            QFrame:hover {
                background-color: #F3F4F6;
            }
        """)
        
        layout = QHBoxLayout(item)
        layout.setContentsMargins(12, 8, 12, 8)
        
        # Temps
        time_label = QLabel(time)
        time_label.setStyleSheet("""
            color: #6B7280;
            font-size: 12px;
            font-weight: 500;
            min-width: 40px;
        """)
        layout.addWidget(time_label)
        
        # Action
        action_label = QLabel(action)
        action_label.setStyleSheet("""
            color: #374151;
            font-size: 14px;
            font-weight: 500;
        """)
        layout.addWidget(action_label)
        
        layout.addStretch()
        
        # Valeur
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            color: {color};
            font-size: 14px;
            font-weight: 600;
        """)
        layout.addWidget(value_label)
        
        return item
        
    def update_data(self, production_data):
        """Met à jour les données du dashboard."""
        # Mettre à jour les cartes principales
        if 'total_production' in production_data:
            self.total_production_card.value = f"{production_data['total_production']:.1f}"
            
        if 'efficiency' in production_data:
            self.efficiency_card.value = f"{production_data['efficiency']:.1f}"
            
        if 'downtime' in production_data:
            self.downtime_card.value = f"{production_data['downtime']:.0f}"
            
        # Mettre à jour les unités
        if 'crushing_production' in production_data:
            self.crushing_card.value = f"{production_data['crushing_production']:.1f}"
            
        if 'washing_production' in production_data:
            self.washing_card.value = f"{production_data['washing_production']:.1f}"
            
        if 'quality' in production_data:
            self.quality_card.value = f"{production_data['quality']:.1f}"

class ModernProductionTheme:
    """Thème moderne pour l'application de production."""
    
    # Couleurs principales (inspirées du design financier)
    PRIMARY_BLUE = "#2E86AB"
    SUCCESS_GREEN = "#10B981"
    WARNING_ORANGE = "#F59E0B"
    DANGER_RED = "#EF4444"
    PURPLE = "#8B5CF6"
    INDIGO = "#3B82F6"
    
    # Couleurs neutres
    GRAY_50 = "#F9FAFB"
    GRAY_100 = "#F3F4F6"
    GRAY_200 = "#E5E7EB"
    GRAY_300 = "#D1D5DB"
    GRAY_400 = "#9CA3AF"
    GRAY_500 = "#6B7280"
    GRAY_600 = "#4B5563"
    GRAY_700 = "#374151"
    GRAY_800 = "#1F2937"
    GRAY_900 = "#111827"
    
    @staticmethod
    def get_card_style():
        """Retourne le style CSS pour les cartes modernes."""
        return f"""
            QFrame {{
                background-color: white;
                border: 1px solid {ModernProductionTheme.GRAY_200};
                border-radius: 12px;
                padding: 16px;
            }}
            QFrame:hover {{
                border: 1px solid {ModernProductionTheme.PRIMARY_BLUE};
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            }}
        """
    
    @staticmethod
    def get_button_style(color=PRIMARY_BLUE):
        """Retourne le style CSS pour les boutons modernes."""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: 600;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
                transform: translateY(-1px);
            }}
            QPushButton:pressed {{
                background-color: {color}bb;
                transform: translateY(0px);
            }}
        """
    
    @staticmethod
    def get_input_style():
        """Retourne le style CSS pour les champs de saisie."""
        return f"""
            QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
                border: 1px solid {ModernProductionTheme.GRAY_300};
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
                background-color: white;
            }}
            QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {{
                border: 2px solid {ModernProductionTheme.PRIMARY_BLUE};
                outline: none;
            }}
        """
