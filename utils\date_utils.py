"""
Utilitaires pour la gestion standardisée des dates dans l'application.
Toutes les dates sont stockées sans heure (00:00:00) pour une cohérence globale.
"""

from datetime import datetime, date, time
from typing import Union, Optional
from PyQt5.QtCore import QDate
import logging

def normalize_date(input_date: Union[datetime, date, QDate, str]) -> datetime:
    """
    Normalise une date en datetime avec heure à 00:00:00.
    
    Args:
        input_date: Date à normaliser (datetime, date, QDate ou string)
        
    Returns:
        datetime: Date normalisée avec heure à 00:00:00
    """
    try:
        if isinstance(input_date, datetime):
            # Si c'est déjà un datetime, garder seulement la date
            return datetime.combine(input_date.date(), time.min)
        elif isinstance(input_date, date):
            # Si c'est un objet date, convertir en datetime
            return datetime.combine(input_date, time.min)
        elif isinstance(input_date, QDate):
            # Si c'est un QDate, convertir en datetime
            py_date = input_date.toPyDate()
            return datetime.combine(py_date, time.min)
        elif isinstance(input_date, str):
            # Si c'est une string, parser et normaliser
            parsed_date = datetime.strptime(input_date, "%Y-%m-%d")
            return datetime.combine(parsed_date.date(), time.min)
        else:
            raise ValueError(f"Type de date non supporté: {type(input_date)}")
    except Exception as e:
        logging.error(f"Erreur normalisation date {input_date}: {str(e)}")
        # Retourner aujourd'hui par défaut
        return datetime.combine(date.today(), time.min)

def get_date_range_for_day(target_date: Union[datetime, date, QDate]) -> tuple[datetime, datetime]:
    """
    Retourne le début et la fin d'une journée pour une date donnée.
    
    Args:
        target_date: Date cible
        
    Returns:
        tuple: (début de journée, fin de journée)
    """
    normalized_date = normalize_date(target_date)
    start_of_day = normalized_date
    end_of_day = datetime.combine(normalized_date.date(), time.max)
    return start_of_day, end_of_day

def format_date_for_display(input_date: Union[datetime, date]) -> str:
    """
    Formate une date pour l'affichage (format français).
    
    Args:
        input_date: Date à formater
        
    Returns:
        str: Date formatée (ex: "27/06/2025")
    """
    try:
        if isinstance(input_date, datetime):
            return input_date.strftime("%d/%m/%Y")
        elif isinstance(input_date, date):
            return input_date.strftime("%d/%m/%Y")
        else:
            return str(input_date)
    except Exception as e:
        logging.error(f"Erreur formatage date {input_date}: {str(e)}")
        return "Date invalide"

def format_datetime_for_display(input_datetime: datetime) -> str:
    """
    Formate un datetime pour l'affichage (format français avec heure).
    
    Args:
        input_datetime: DateTime à formater
        
    Returns:
        str: DateTime formaté (ex: "27/06/2025 14:30")
    """
    try:
        return input_datetime.strftime("%d/%m/%Y %H:%M")
    except Exception as e:
        logging.error(f"Erreur formatage datetime {input_datetime}: {str(e)}")
        return "DateTime invalide"

def is_same_day(date1: Union[datetime, date], date2: Union[datetime, date]) -> bool:
    """
    Vérifie si deux dates correspondent au même jour.
    
    Args:
        date1: Première date
        date2: Deuxième date
        
    Returns:
        bool: True si même jour, False sinon
    """
    try:
        if isinstance(date1, datetime):
            date1 = date1.date()
        if isinstance(date2, datetime):
            date2 = date2.date()
        return date1 == date2
    except Exception as e:
        logging.error(f"Erreur comparaison dates {date1}, {date2}: {str(e)}")
        return False

def get_today_normalized() -> datetime:
    """
    Retourne la date d'aujourd'hui normalisée (00:00:00).
    
    Returns:
        datetime: Aujourd'hui à 00:00:00
    """
    return datetime.combine(date.today(), time.min)

def get_yesterday_normalized() -> datetime:
    """
    Retourne la date d'hier normalisée (00:00:00).
    
    Returns:
        datetime: Hier à 00:00:00
    """
    from datetime import timedelta
    yesterday = date.today() - timedelta(days=1)
    return datetime.combine(yesterday, time.min)

def get_week_start_normalized(target_date: Optional[Union[datetime, date]] = None) -> datetime:
    """
    Retourne le début de la semaine pour une date donnée (lundi 00:00:00).
    
    Args:
        target_date: Date cible (par défaut: aujourd'hui)
        
    Returns:
        datetime: Début de semaine normalisé
    """
    if target_date is None:
        target_date = date.today()
    elif isinstance(target_date, datetime):
        target_date = target_date.date()
    
    # Calculer le lundi de la semaine
    days_since_monday = target_date.weekday()
    monday = target_date - timedelta(days=days_since_monday)
    return datetime.combine(monday, time.min)

def get_month_start_normalized(target_date: Optional[Union[datetime, date]] = None) -> datetime:
    """
    Retourne le début du mois pour une date donnée (1er du mois 00:00:00).
    
    Args:
        target_date: Date cible (par défaut: aujourd'hui)
        
    Returns:
        datetime: Début de mois normalisé
    """
    if target_date is None:
        target_date = date.today()
    elif isinstance(target_date, datetime):
        target_date = target_date.date()
    
    first_of_month = target_date.replace(day=1)
    return datetime.combine(first_of_month, time.min)

# Import pour compatibilité
from datetime import timedelta

__all__ = [
    'normalize_date',
    'get_date_range_for_day', 
    'format_date_for_display',
    'format_datetime_for_display',
    'is_same_day',
    'get_today_normalized',
    'get_yesterday_normalized',
    'get_week_start_normalized',
    'get_month_start_normalized'
]
