"""
Onglet de dashboard moderne inspiré du design financier pour SOTRAMINE.
Interface moderne avec cartes, métriques et visualisations.
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, 
                            QFrame, QScrollArea, QWidget, QPushButton, QSplitter)
from PyQt5.QtCore import Qt, QTimer, QDate
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor, QBrush
from datetime import datetime, timedelta

from .base_optimized import BaseOptimizedTab
from ui.design.modern_dashboard_theme import ModernCard, ProductionOverviewWidget, ModernProductionTheme
from models.enums import ProcessStepEnum
from utils.date_utils import normalize_date, format_date_for_display

class ModernDashboardTab(BaseOptimizedTab):
    """Dashboard moderne pour la vue d'ensemble de la production."""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "🏠 Dashboard", parent)
        self.setup_interface()
        self.setup_timers()
        
    def setup_interface(self):
        """Configure l'interface moderne du dashboard."""
        # Zone de défilement principale
        main_scroll = QScrollArea()
        main_scroll.setWidgetResizable(True)
        main_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        main_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        main_scroll.setStyleSheet(f"""
            QScrollArea {{
                background-color: {ModernProductionTheme.GRAY_50};
                border: none;
            }}
        """)
        self.main_layout.addWidget(main_scroll)
        
        # Widget contenu principal
        main_content = QWidget()
        main_layout = QVBoxLayout(main_content)
        main_layout.setSpacing(24)
        main_layout.setContentsMargins(24, 24, 24, 24)
        
        main_scroll.setWidget(main_content)
        
        # En-tête du dashboard
        self.setup_dashboard_header(main_layout)
        
        # Métriques principales
        self.setup_main_metrics(main_layout)
        
        # Section unités de production
        self.setup_production_units(main_layout)
        
        # Section activité et tendances
        self.setup_activity_trends(main_layout)
        
        # Charger les données initiales
        self.refresh_dashboard_data()
        
    def setup_dashboard_header(self, layout):
        """Configure l'en-tête du dashboard."""
        header_container = QFrame()
        header_container.setStyleSheet(f"""
            QFrame {{
                background: linear-gradient(135deg, {ModernProductionTheme.PRIMARY_BLUE} 0%, {ModernProductionTheme.INDIGO} 100%);
                border-radius: 16px;
                padding: 24px;
                color: white;
            }}
        """)
        
        header_layout = QHBoxLayout(header_container)
        header_layout.setContentsMargins(24, 24, 24, 24)
        
        # Informations principales
        info_layout = QVBoxLayout()
        
        # Titre principal
        title = QLabel("SOTRAMINE - Production Phosphate")
        title.setStyleSheet("""
            color: white;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        """)
        info_layout.addWidget(title)
        
        # Sous-titre avec date
        today = datetime.now()
        subtitle = QLabel(f"Dashboard Production - {today.strftime('%A %d %B %Y')}")
        subtitle.setStyleSheet("""
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            font-weight: 400;
        """)
        info_layout.addWidget(subtitle)
        
        header_layout.addLayout(info_layout)
        header_layout.addStretch()
        
        # Statut système
        status_layout = QVBoxLayout()
        
        status_label = QLabel("🟢 Système Opérationnel")
        status_label.setStyleSheet("""
            color: white;
            font-size: 14px;
            font-weight: 600;
            text-align: right;
        """)
        status_layout.addWidget(status_label)
        
        last_update = QLabel(f"Dernière MAJ: {datetime.now().strftime('%H:%M')}")
        last_update.setStyleSheet("""
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            text-align: right;
        """)
        status_layout.addWidget(last_update)
        
        header_layout.addLayout(status_layout)
        
        layout.addWidget(header_container)
        
    def setup_main_metrics(self, layout):
        """Configure les métriques principales."""
        metrics_title = QLabel("📊 Métriques Principales")
        metrics_title.setStyleSheet(f"""
            color: {ModernProductionTheme.GRAY_900};
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 16px;
        """)
        layout.addWidget(metrics_title)
        
        # Grille de métriques
        metrics_grid = QGridLayout()
        metrics_grid.setSpacing(20)
        
        # Production totale
        self.total_production_card = ModernCard(
            "Production Totale Jour",
            "0.0",
            "T",
            "Objectif: 500T",
            ModernProductionTheme.PRIMARY_BLUE
        )
        metrics_grid.addWidget(self.total_production_card, 0, 0)
        
        # Efficacité globale
        self.efficiency_card = ModernCard(
            "Efficacité Globale",
            "0.0",
            "%",
            "Cible: >85%",
            ModernProductionTheme.SUCCESS_GREEN
        )
        metrics_grid.addWidget(self.efficiency_card, 0, 1)
        
        # Qualité moyenne
        self.quality_card = ModernCard(
            "Qualité Moyenne",
            "0.0",
            "% P2O5",
            "Standard: >30%",
            ModernProductionTheme.PURPLE
        )
        metrics_grid.addWidget(self.quality_card, 0, 2)
        
        # Temps d'arrêt
        self.downtime_card = ModernCard(
            "Temps d'Arrêt Total",
            "0",
            "min",
            "Objectif: <120min",
            ModernProductionTheme.DANGER_RED
        )
        metrics_grid.addWidget(self.downtime_card, 0, 3)
        
        layout.addLayout(metrics_grid)
        
    def setup_production_units(self, layout):
        """Configure la section des unités de production."""
        units_title = QLabel("🏭 Unités de Production")
        units_title.setStyleSheet(f"""
            color: {ModernProductionTheme.GRAY_900};
            font-size: 20px;
            font-weight: 700;
            margin-top: 24px;
            margin-bottom: 16px;
        """)
        layout.addWidget(units_title)
        
        # Container pour les unités
        units_container = QFrame()
        units_container.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid {ModernProductionTheme.GRAY_200};
                border-radius: 16px;
                padding: 24px;
            }}
        """)
        
        units_layout = QHBoxLayout(units_container)
        units_layout.setSpacing(24)
        
        # Unité Concassage
        crushing_section = self.create_unit_section(
            "🔨 Concassage",
            "Préparation matière première",
            ModernProductionTheme.WARNING_ORANGE
        )
        units_layout.addWidget(crushing_section)
        
        # Séparateur
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setStyleSheet(f"color: {ModernProductionTheme.GRAY_200};")
        units_layout.addWidget(separator)
        
        # Unité Laverie
        washing_section = self.create_unit_section(
            "💧 Laverie",
            "Enrichissement phosphate",
            ModernProductionTheme.INDIGO
        )
        units_layout.addWidget(washing_section)
        
        layout.addWidget(units_container)
        
    def create_unit_section(self, title, description, color):
        """Crée une section pour une unité de production."""
        section = QFrame()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(16)
        
        # En-tête de l'unité
        header_layout = QHBoxLayout()
        
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            color: {color};
            font-size: 18px;
            font-weight: 700;
        """)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        status_indicator = QLabel("🟢")
        status_indicator.setStyleSheet("font-size: 16px;")
        header_layout.addWidget(status_indicator)
        
        section_layout.addLayout(header_layout)
        
        # Description
        desc_label = QLabel(description)
        desc_label.setStyleSheet(f"""
            color: {ModernProductionTheme.GRAY_600};
            font-size: 14px;
            margin-bottom: 12px;
        """)
        section_layout.addWidget(desc_label)
        
        # Métriques de l'unité
        metrics_layout = QVBoxLayout()
        metrics_layout.setSpacing(8)
        
        # Production
        prod_layout = QHBoxLayout()
        prod_layout.addWidget(QLabel("Production:"))
        prod_value = QLabel("0.0 T")
        prod_value.setStyleSheet(f"color: {color}; font-weight: 600;")
        prod_layout.addWidget(prod_value)
        prod_layout.addStretch()
        metrics_layout.addLayout(prod_layout)
        
        # Heures actives
        hours_layout = QHBoxLayout()
        hours_layout.addWidget(QLabel("Heures actives:"))
        hours_value = QLabel("0.0 h")
        hours_value.setStyleSheet(f"color: {color}; font-weight: 600;")
        hours_layout.addWidget(hours_value)
        hours_layout.addStretch()
        metrics_layout.addLayout(hours_layout)
        
        # Rendement
        yield_layout = QHBoxLayout()
        yield_layout.addWidget(QLabel("Rendement:"))
        yield_value = QLabel("0.0 T/h")
        yield_value.setStyleSheet(f"color: {color}; font-weight: 600;")
        yield_layout.addWidget(yield_value)
        yield_layout.addStretch()
        metrics_layout.addLayout(yield_layout)
        
        section_layout.addLayout(metrics_layout)
        
        # Stocker les références pour mise à jour
        if "Concassage" in title:
            self.crushing_prod_label = prod_value
            self.crushing_hours_label = hours_value
            self.crushing_yield_label = yield_value
        else:
            self.washing_prod_label = prod_value
            self.washing_hours_label = hours_value
            self.washing_yield_label = yield_value
        
        return section
        
    def setup_activity_trends(self, layout):
        """Configure la section d'activité et tendances."""
        activity_title = QLabel("📈 Activité Récente & Tendances")
        activity_title.setStyleSheet(f"""
            color: {ModernProductionTheme.GRAY_900};
            font-size: 20px;
            font-weight: 700;
            margin-top: 24px;
            margin-bottom: 16px;
        """)
        layout.addWidget(activity_title)
        
        # Splitter horizontal pour activité et tendances
        activity_splitter = QSplitter(Qt.Horizontal)
        
        # Section activité récente
        activity_widget = self.create_recent_activity_widget()
        activity_splitter.addWidget(activity_widget)
        
        # Section tendances
        trends_widget = self.create_trends_widget()
        activity_splitter.addWidget(trends_widget)
        
        activity_splitter.setSizes([400, 400])
        layout.addWidget(activity_splitter)
        
    def create_recent_activity_widget(self):
        """Crée le widget d'activité récente."""
        widget = QFrame()
        widget.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid {ModernProductionTheme.GRAY_200};
                border-radius: 16px;
                padding: 20px;
            }}
        """)
        
        layout = QVBoxLayout(widget)
        
        title = QLabel("📋 Activité Récente")
        title.setStyleSheet(f"""
            color: {ModernProductionTheme.GRAY_900};
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
        """)
        layout.addWidget(title)
        
        # Liste d'activités (simulées)
        activities = [
            {"time": "14:30", "action": "Production laverie", "value": "+125.5 T", "color": ModernProductionTheme.SUCCESS_GREEN},
            {"time": "13:45", "action": "Maintenance préventive", "value": "30 min", "color": ModernProductionTheme.WARNING_ORANGE},
            {"time": "12:15", "action": "Production concassage", "value": "+200.0 T", "color": ModernProductionTheme.PRIMARY_BLUE},
            {"time": "11:30", "action": "Consommation eau", "value": "45 m³", "color": ModernProductionTheme.INDIGO},
        ]
        
        for activity in activities:
            activity_item = self.create_activity_item(activity)
            layout.addWidget(activity_item)
            
        return widget
        
    def create_activity_item(self, activity):
        """Crée un élément d'activité."""
        item = QFrame()
        item.setStyleSheet(f"""
            QFrame {{
                background-color: {ModernProductionTheme.GRAY_50};
                border-radius: 8px;
                padding: 12px;
                margin-bottom: 8px;
            }}
            QFrame:hover {{
                background-color: {ModernProductionTheme.GRAY_100};
            }}
        """)
        
        layout = QHBoxLayout(item)
        layout.setContentsMargins(12, 8, 12, 8)
        
        # Temps
        time_label = QLabel(activity["time"])
        time_label.setStyleSheet(f"""
            color: {ModernProductionTheme.GRAY_500};
            font-size: 12px;
            font-weight: 500;
            min-width: 40px;
        """)
        layout.addWidget(time_label)
        
        # Action
        action_label = QLabel(activity["action"])
        action_label.setStyleSheet(f"""
            color: {ModernProductionTheme.GRAY_700};
            font-size: 14px;
            font-weight: 500;
        """)
        layout.addWidget(action_label)
        
        layout.addStretch()
        
        # Valeur
        value_label = QLabel(activity["value"])
        value_label.setStyleSheet(f"""
            color: {activity["color"]};
            font-size: 14px;
            font-weight: 600;
        """)
        layout.addWidget(value_label)
        
        return item
        
    def create_trends_widget(self):
        """Crée le widget de tendances."""
        widget = QFrame()
        widget.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid {ModernProductionTheme.GRAY_200};
                border-radius: 16px;
                padding: 20px;
            }}
        """)
        
        layout = QVBoxLayout(widget)
        
        title = QLabel("📊 Tendances 7 Jours")
        title.setStyleSheet(f"""
            color: {ModernProductionTheme.GRAY_900};
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
        """)
        layout.addWidget(title)
        
        # Métriques de tendance
        trends = [
            {"label": "Production moyenne", "value": "425.3 T/jour", "trend": "+5.2%", "positive": True},
            {"label": "Efficacité moyenne", "value": "87.4%", "trend": "+2.1%", "positive": True},
            {"label": "Temps d'arrêt moyen", "value": "95 min/jour", "trend": "-12.5%", "positive": True},
            {"label": "Qualité moyenne", "value": "31.2% P2O5", "trend": "+0.8%", "positive": True},
        ]
        
        for trend in trends:
            trend_item = self.create_trend_item(trend)
            layout.addWidget(trend_item)
            
        return widget
        
    def create_trend_item(self, trend):
        """Crée un élément de tendance."""
        item = QFrame()
        item.setStyleSheet(f"""
            QFrame {{
                background-color: {ModernProductionTheme.GRAY_50};
                border-radius: 8px;
                padding: 12px;
                margin-bottom: 8px;
            }}
        """)
        
        layout = QVBoxLayout(item)
        layout.setSpacing(4)
        
        # En-tête avec label et tendance
        header_layout = QHBoxLayout()
        
        label = QLabel(trend["label"])
        label.setStyleSheet(f"""
            color: {ModernProductionTheme.GRAY_700};
            font-size: 14px;
            font-weight: 500;
        """)
        header_layout.addWidget(label)
        header_layout.addStretch()
        
        trend_color = ModernProductionTheme.SUCCESS_GREEN if trend["positive"] else ModernProductionTheme.DANGER_RED
        trend_icon = "📈" if trend["positive"] else "📉"
        
        trend_label = QLabel(f"{trend_icon} {trend['trend']}")
        trend_label.setStyleSheet(f"""
            color: {trend_color};
            font-size: 12px;
            font-weight: 600;
        """)
        header_layout.addWidget(trend_label)
        
        layout.addLayout(header_layout)
        
        # Valeur
        value_label = QLabel(trend["value"])
        value_label.setStyleSheet(f"""
            color: {ModernProductionTheme.GRAY_900};
            font-size: 16px;
            font-weight: 700;
        """)
        layout.addWidget(value_label)
        
        return item
        
    def setup_timers(self):
        """Configure les timers pour les mises à jour automatiques."""
        # Timer pour actualisation des données
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_dashboard_data)
        self.refresh_timer.start(30000)  # 30 secondes
        
    def refresh_dashboard_data(self):
        """Actualise les données du dashboard."""
        try:
            today = normalize_date(datetime.now().date())
            start_datetime, end_datetime = today, today.replace(hour=23, minute=59, second=59)
            
            # Récupérer les productions du jour
            crushing_productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.CONCASSAGE,
                start_date=start_datetime,
                end_date=end_datetime
            )
            
            washing_productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.LAVERIE,
                start_date=start_datetime,
                end_date=end_datetime
            )
            
            # Calculer les métriques
            crushing_total = sum(p['quantity'] for p in crushing_productions)
            washing_total = sum(p['quantity'] for p in washing_productions)
            total_production = washing_total  # Production finale = laverie
            
            # Mettre à jour les cartes
            self.total_production_card.value = f"{total_production:.1f}"
            
            # Calculer efficacité
            total_consumed = sum(p.get('quantity_used', 0) or 0 for p in crushing_productions)
            if total_consumed > 0:
                efficiency = (total_production / total_consumed) * 100
                self.efficiency_card.value = f"{efficiency:.1f}"
            
            # Qualité moyenne
            if washing_productions:
                qualities = []
                for p in washing_productions:
                    if p.get('quality'):
                        try:
                            quality_val = float(str(p['quality']).replace('%', '').strip())
                            qualities.append(quality_val)
                        except:
                            pass
                if qualities:
                    avg_quality = sum(qualities) / len(qualities)
                    self.quality_card.value = f"{avg_quality:.1f}"
            
            # Mettre à jour les sections d'unités
            self.crushing_prod_label.setText(f"{crushing_total:.1f} T")
            self.washing_prod_label.setText(f"{washing_total:.1f} T")
            
            # Heures de production
            crushing_hours = sum(p.get('production_hours', 0) or 0 for p in crushing_productions)
            washing_hours = sum(p.get('production_hours', 0) or 0 for p in washing_productions)
            
            self.crushing_hours_label.setText(f"{crushing_hours:.1f} h")
            self.washing_hours_label.setText(f"{washing_hours:.1f} h")
            
            # Rendements
            if crushing_hours > 0:
                crushing_yield = crushing_total / crushing_hours
                self.crushing_yield_label.setText(f"{crushing_yield:.1f} T/h")
                
            if washing_hours > 0:
                washing_yield = washing_total / washing_hours
                self.washing_yield_label.setText(f"{washing_yield:.1f} T/h")
                
        except Exception as e:
            print(f"Erreur actualisation dashboard: {str(e)}")
            
    def refresh_tab(self):
        """Méthode appelée lors du changement d'onglet."""
        self.refresh_dashboard_data()
