#!/usr/bin/env python3
"""
Script de test pour démontrer les améliorations d'interface avec défilement
pour éviter l'encombrement de l'onglet production unifié.
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt
from database.database_manager import DatabaseManager
from ui.tabs.unified_production_tab import UnifiedProductionTab

class TestScrollableInterface(QMainWindow):
    """Fenêtre de test pour l'interface avec défilement."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Interface Production avec Défilement")
        self.setGeometry(100, 100, 1200, 800)
        
        # Initialiser la base de données
        self.db_manager = DatabaseManager()
        self.db_manager.initialize_database()
        
        # Créer l'onglet unifié
        self.unified_tab = UnifiedProductionTab(self.db_manager)
        
        # Configuration de la fenêtre
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Titre
        title = QLabel("🧪 Test Interface Production avec Défilement")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Ajouter l'onglet unifié
        layout.addWidget(self.unified_tab)
        
        print("✅ Interface de test créée avec succès!")
        print("\n🎯 Améliorations testées:")
        print("  📜 Zone gauche avec défilement vertical")
        print("  📊 Zone droite avec défilement pour les tableaux")
        print("  📅 Section date compacte avec navigation")
        print("  📈 KPI optimisés avec hauteur limitée")
        print("  🔧 Onglets d'unités avec défilement interne")
        print("  ⏹️ Section arrêts compacte")
        print("  🎨 Interface responsive et adaptative")

def test_scrollable_interface():
    """Test de l'interface avec défilement."""
    print("🖥️ Test de l'interface production avec défilement")
    print("=" * 55)
    
    app = QApplication(sys.argv)
    
    # Créer la fenêtre de test
    test_window = TestScrollableInterface()
    test_window.show()
    
    print("\n📋 Instructions de test:")
    print("1. Redimensionnez la fenêtre pour tester la responsivité")
    print("2. Testez le défilement dans la zone gauche (contrôles)")
    print("3. Testez le défilement dans la zone droite (tableaux)")
    print("4. Naviguez entre les dates avec les boutons")
    print("5. Changez d'onglet entre Concassage et Laverie")
    print("6. Vérifiez que tous les éléments sont accessibles")
    
    print("\n🎨 Améliorations d'interface:")
    print("  ✅ Défilement vertical pour éviter l'encombrement")
    print("  ✅ Sections compactes avec hauteurs optimisées")
    print("  ✅ Libellés raccourcis pour économiser l'espace")
    print("  ✅ Widgets redimensionnés intelligemment")
    print("  ✅ Marges et espacements optimisés")
    print("  ✅ Interface adaptative aux différentes tailles d'écran")
    
    print("\n🚀 Fonctionnalités préservées:")
    print("  ✅ Toutes les fonctionnalités de saisie")
    print("  ✅ Navigation par date")
    print("  ✅ KPI en temps réel")
    print("  ✅ Tableaux de données")
    print("  ✅ Gestion des arrêts")
    print("  ✅ Logique de production corrigée")
    
    # Lancer l'application
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_scrollable_interface()
