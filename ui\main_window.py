from PyQt5.QtWidgets import (Q<PERSON><PERSON><PERSON><PERSON>ow, QTabWidget, QWidget, QVBoxLayout,
                             QMessageBox, QApplication, QStatusBar, QLabel,
                             QMenu, QAction, QSplashScreen)
from PyQt5.QtCore import Qt, QTimer, QDateTime
from PyQt5.QtGui import QIcon, QPixmap, QFont
from ui.professional_theme import get_professional_style, SotramineTheme
from ui.icon_manager import icon_manager
from .tabs.optimized_reception_tab import OptimizedReceptionTab
from .tabs.optimized_crushing_tab import OptimizedCrushingTab
from .tabs.optimized_laverie_tab import OptimizedLaverieTab
from .tabs.simple_stock_tab import OptimizedStockTab
from .tabs.optimized_reports_tab import OptimizedReportsTab
from .tabs.hour_counter_tab import HourCounterTab
from .tabs.consumption_tab import ConsumptionTab
from .tabs.unified_production_tab import UnifiedProductionTab
from .tabs.production_history_tab import ProductionHistoryTab
from .tabs.modern_dashboard_tab import ModernDashboardTab
from .tabs.settings_tab import SettingsTab
import sys
import os

class MainWindow(QMainWindow):
    """Fenêtre principale de l'application."""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.init_ui()
    
    def init_ui(self):
        """Initialise l'interface utilisateur."""
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Suivi de Production")
        
        # Obtenir les dimensions de l'écran
        screen_geometry = QApplication.desktop().availableGeometry()
        screen_width = screen_geometry.width()
        screen_height = screen_geometry.height()
        
        # Définir une taille de fenêtre qui respecte les limites de l'écran
        window_width = min(1400, screen_width - 100)
        window_height = min(900, screen_height - 100)
        
        # Centrer la fenêtre sur l'écran
        x_position = (screen_width - window_width) // 2
        y_position = (screen_height - window_height) // 2
        
        self.setGeometry(x_position, y_position, window_width, window_height)
        
        # Appliquer le thème professionnel
        self.setStyleSheet(get_professional_style())
        
        # Définir l'icône de l'application
        if os.path.exists("logo.png"):
            self.setWindowIcon(QIcon("logo.png"))
        
        # Créer le widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Créer les onglets
        self.tabs = QTabWidget()
        
        # Créer le dossier d'icônes s'il n'existe pas
        icons_dir = os.path.join("resources", "icons")
        if not os.path.exists(icons_dir):
            os.makedirs(icons_dir, exist_ok=True)
        
        # Ajouter les onglets avec thème professionnel
        self.modern_dashboard_tab = ModernDashboardTab(self.db_manager)  # Dashboard moderne
        self.reception_tab = OptimizedReceptionTab(self.db_manager)
        self.unified_production_tab = UnifiedProductionTab(self.db_manager)  # Nouvel onglet unifié
        self.production_history_tab = ProductionHistoryTab(self.db_manager)  # Onglet historique
        self.hour_counter_tab = HourCounterTab(self.db_manager)  # Onglet compteurs horaires réactivé
        self.stock_tab = OptimizedStockTab(self.db_manager)
        self.reports_tab = OptimizedReportsTab(self.db_manager)
        self.settings_tab = SettingsTab(db_manager=self.db_manager)

        # Conserver les anciens onglets pour référence (commentés)
        # self.crushing_tab = OptimizedCrushingTab(self.db_manager)
        # self.laverie_tab = OptimizedLaverieTab(self.db_manager)
        # self.consumption_tab = ConsumptionTab(self.db_manager)

        # Ajouter les onglets avec des icônes professionnelles
        self.tabs.addTab(self.modern_dashboard_tab, icon_manager.get_tab_icon("Dashboard"), "Dashboard")
        self.tabs.addTab(self.reception_tab, icon_manager.get_tab_icon("Réception"), "Réception")
        self.tabs.addTab(self.unified_production_tab, icon_manager.get_tab_icon("Production"), "Production")
        self.tabs.addTab(self.production_history_tab, icon_manager.get_tab_icon("Historique"), "Historique")
        self.tabs.addTab(self.hour_counter_tab, icon_manager.get_tab_icon("Compteurs Horaires"), "Compteurs Horaires")
        self.tabs.addTab(self.stock_tab, icon_manager.get_tab_icon("Stocks"), "Stocks")
        self.tabs.addTab(self.reports_tab, icon_manager.get_tab_icon("Rapports"), "Rapports")
        self.tabs.addTab(self.settings_tab, icon_manager.get_tab_icon("Paramètres"), "Paramètres")

        # Anciens onglets individuels (commentés pour le moment)
        # self.tabs.addTab(self.crushing_tab, icon_manager.get_tab_icon("Concassage"), "Concassage")
        # self.tabs.addTab(self.laverie_tab, icon_manager.get_tab_icon("Laverie"), "Laverie")
        # self.tabs.addTab(self.consumption_tab, icon_manager.get_tab_icon("Consommations"), "Consommations")
        
        layout.addWidget(self.tabs)
        
        # Ajouter une barre d'état professionnelle
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Ajouter un label pour l'entreprise
        company_label = QLabel("SOTRAMINE PHOSPHATE")
        company_label.setStyleSheet(f"""
            color: {SotramineTheme.TEXT_WHITE};
            font-weight: bold;
            padding: 0 10px;
        """)
        self.status_bar.addWidget(company_label)
        
        # Ajouter un séparateur
        separator = QLabel("|")
        separator.setStyleSheet(f"color: {SotramineTheme.TEXT_WHITE}; padding: 0 5px;")
        self.status_bar.addWidget(separator)
        
        # Ajouter un label pour la date et l'heure
        self.date_time_label = QLabel()
        self.date_time_label.setStyleSheet(f"""
            color: {SotramineTheme.TEXT_WHITE};
            font-weight: bold;
            padding: 0 10px;
        """)
        self.status_bar.addPermanentWidget(self.date_time_label)
        
        # Timer pour mettre à jour la date et l'heure
        self.date_time_timer = QTimer()
        self.date_time_timer.timeout.connect(self.update_date_time)
        self.date_time_timer.start(1000)  # Mise à jour toutes les secondes
        self.update_date_time()
        
        # Connecter le signal de changement d'onglet
        self.tabs.currentChanged.connect(self.on_tab_changed)
    
    def on_tab_changed(self, index):
        """Gère le changement d'onglet."""
        current_tab = self.tabs.widget(index)
        if hasattr(current_tab, 'refresh_tab'):
            current_tab.refresh_tab()
    
    def show_splash_screen(self):
        """Affiche un écran de démarrage professionnel."""
        if os.path.exists("logo.png"):
            pixmap = QPixmap("logo.png")
            splash = QSplashScreen(pixmap)
            splash.show()
            return splash
        return None
    
    def update_date_time(self):
        """Met à jour l'affichage de la date et de l'heure."""
        current_datetime = QDateTime.currentDateTime()
        formatted_datetime = current_datetime.toString("dddd dd MMMM yyyy - hh:mm:ss")
        self.date_time_label.setText(formatted_datetime)
    
    def closeEvent(self, event):
        """Gère la fermeture de l'application."""
        reply = QMessageBox.question(
            self, 'Confirmation',
            "Voulez-vous vraiment quitter l'application ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()

def main():
    """Point d'entrée de l'application."""
    from database.database_manager import DatabaseManager
    app = QApplication(sys.argv)
    db_manager = DatabaseManager()
    window = MainWindow(db_manager)
    window.show()
    sys.exit(app.exec_())