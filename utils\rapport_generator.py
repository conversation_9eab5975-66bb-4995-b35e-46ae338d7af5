"""
Générateur de rapports de production pour SOTRAMINE.
Crée des rapports automatiques basés sur les données de la base.
"""

from datetime import datetime, timedelta, date
from typing import Dict, List, Any
import os
import json

class RapportProductionGenerator:
    """Générateur de rapports de production."""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.template_path = "templates/rapport_production_template.md"
        
    def generer_rapport_journalier(self, date_production: date = None) -> str:
        """
        Génère un rapport de production journalier.
        
        Args:
            date_production: Date pour le rapport (par défaut: aujourd'hui)
            
        Returns:
            str: Contenu du rapport en markdown
        """
        if date_production is None:
            date_production = date.today()
            
        # Collecter toutes les données
        donnees = self._collecter_donnees(date_production)
        
        # Charger le template
        template = self._charger_template()
        
        # Remplacer les variables
        rapport = self._remplacer_variables(template, donnees)
        
        return rapport
        
    def _collecter_donnees(self, date_production: date) -> Dict[str, Any]:
        """Collecte toutes les données nécessaires pour le rapport."""
        from models.enums import ProcessStepEnum, ResourceType
        from utils.date_utils import normalize_date
        
        # Période de la journée
        start_datetime = normalize_date(date_production)
        end_datetime = start_datetime.replace(hour=23, minute=59, second=59)
        
        donnees = {}
        
        # === INFORMATIONS GÉNÉRALES ===
        donnees.update(self._get_infos_generales(date_production))
        
        # === DONNÉES DE PRODUCTION ===
        donnees.update(self._get_donnees_production(start_datetime, end_datetime))
        
        # === DONNÉES DE CONSOMMATION ===
        donnees.update(self._get_donnees_consommation(start_datetime, end_datetime))
        
        # === DONNÉES D'ARRÊTS ===
        donnees.update(self._get_donnees_arrets(start_datetime, end_datetime))
        
        # === ANALYSES ET TENDANCES ===
        donnees.update(self._get_analyses_tendances(date_production))
        
        # === CALCULS ÉCONOMIQUES ===
        donnees.update(self._get_calculs_economiques(donnees))
        
        # === DONNÉES QUALITÉ ===
        donnees.update(self._get_donnees_qualite(start_datetime, end_datetime))
        
        return donnees
        
    def _get_infos_generales(self, date_production: date) -> Dict[str, Any]:
        """Récupère les informations générales."""
        jours_fr = ["Lundi", "Mardi", "Mercredi", "Jeudi", "Vendredi", "Samedi", "Dimanche"]
        mois_fr = ["", "Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
                   "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"]
        
        jour_semaine = jours_fr[date_production.weekday()]
        mois = mois_fr[date_production.month]
        
        return {
            'date_production': f"{jour_semaine} {date_production.day} {mois} {date_production.year}",
            'equipe': "Équipe A - Poste de Jour",  # À récupérer de la DB si disponible
            'chef_equipe': "Ahmed BENALI",  # À récupérer de la DB
            'heure_debut': "06:00",
            'heure_fin': "18:00",
            'meteo': "Ensoleillé",  # À intégrer avec API météo si souhaité
            'temperature': "28",
            'date_generation': datetime.now().strftime("%d/%m/%Y à %H:%M"),
            'version_rapport': "v2.1.0"
        }
        
    def _get_donnees_production(self, start_datetime: datetime, end_datetime: datetime) -> Dict[str, Any]:
        """Récupère les données de production."""
        from models.enums import ProcessStepEnum
        
        # Productions concassage
        crushing_productions = self.db_manager.get_production_summary(
            step=ProcessStepEnum.CONCASSAGE,
            start_date=start_datetime,
            end_date=end_datetime
        )
        
        # Productions laverie
        washing_productions = self.db_manager.get_production_summary(
            step=ProcessStepEnum.LAVERIE,
            start_date=start_datetime,
            end_date=end_datetime
        )
        
        # Calculs concassage
        concassage_produit = sum(p['quantity'] for p in crushing_productions)
        concassage_consomme = sum(p.get('quantity_used', 0) or 0 for p in crushing_productions)
        concassage_heures = sum(p.get('production_hours', 0) or 0 for p in crushing_productions)
        
        # Calculs laverie
        laverie_produit = sum(p['quantity'] for p in washing_productions)
        laverie_consomme = sum(p.get('quantity_used', 0) or 0 for p in washing_productions)
        laverie_heures = sum(p.get('production_hours', 0) or 0 for p in washing_productions)
        
        # Qualités
        concassage_qualite = self._calculer_qualite_moyenne(crushing_productions)
        laverie_qualite_sortie = self._calculer_qualite_moyenne(washing_productions)
        
        # Calculs dérivés
        concassage_rendement = concassage_produit / concassage_heures if concassage_heures > 0 else 0
        laverie_rendement = laverie_produit / laverie_heures if laverie_heures > 0 else 0
        
        concassage_efficacite = (concassage_produit / concassage_consomme * 100) if concassage_consomme > 0 else 0
        laverie_efficacite = (laverie_produit / laverie_consomme * 100) if laverie_consomme > 0 else 0
        
        # Production totale = laverie (produit final)
        production_realisee = laverie_produit
        
        # Objectifs (à configurer)
        objectif_production = 500.0
        objectif_qualite = 30.0
        objectif_efficacite = 85.0
        
        # Calculs d'écarts
        ecart_production = production_realisee - objectif_production
        pourcentage_objectif = (production_realisee / objectif_production * 100) if objectif_production > 0 else 0
        
        # Efficacité globale
        efficacite_globale = (production_realisee / concassage_consomme * 100) if concassage_consomme > 0 else 0
        
        return {
            # Objectifs vs Réalisé
            'objectif_production': f"{objectif_production:.1f}",
            'production_realisee': f"{production_realisee:.1f}",
            'ecart_production': f"{ecart_production:+.1f}",
            'pourcentage_objectif': f"{pourcentage_objectif:.1f}",
            
            'objectif_qualite': f"{objectif_qualite:.1f}",
            'qualite_realisee': f"{laverie_qualite_sortie:.1f}",
            'ecart_qualite': f"{laverie_qualite_sortie - objectif_qualite:+.1f}",
            'statut_qualite': "✅ Conforme" if laverie_qualite_sortie >= objectif_qualite else "❌ Non conforme",
            
            'objectif_efficacite': f"{objectif_efficacite:.1f}",
            'efficacite_realisee': f"{efficacite_globale:.1f}",
            'ecart_efficacite': f"{efficacite_globale - objectif_efficacite:+.1f}",
            'statut_efficacite': "✅ Excellent" if efficacite_globale >= objectif_efficacite else "⚠️ À améliorer",
            
            # Concassage
            'concassage_produit': f"{concassage_produit:.1f}",
            'concassage_consomme': f"{concassage_consomme:.1f}",
            'concassage_heures': f"{concassage_heures:.1f}",
            'concassage_rendement': f"{concassage_rendement:.1f}",
            'concassage_efficacite': f"{concassage_efficacite:.1f}",
            'concassage_qualite': f"{concassage_qualite:.1f}",
            
            # Laverie
            'laverie_produit': f"{laverie_produit:.1f}",
            'laverie_consomme': f"{laverie_consomme:.1f}",
            'laverie_heures': f"{laverie_heures:.1f}",
            'laverie_rendement': f"{laverie_rendement:.1f}",
            'laverie_efficacite': f"{laverie_efficacite:.1f}",
            'laverie_qualite_entree': f"{concassage_qualite:.1f}",  # Approximation
            'laverie_qualite_sortie': f"{laverie_qualite_sortie:.1f}",
            'taux_enrichissement': f"{laverie_qualite_sortie - concassage_qualite:.1f}",
        }
        
    def _get_donnees_consommation(self, start_datetime: datetime, end_datetime: datetime) -> Dict[str, Any]:
        """Récupère les données de consommation."""
        from models.enums import ResourceType, ProcessStepEnum
        
        # Récupérer toutes les consommations
        consumptions = self.db_manager.get_resource_consumptions(
            start_date=start_datetime,
            end_date=end_datetime
        )
        
        # Initialiser les totaux
        concassage_energie = 0
        laverie_eau = 0
        laverie_reactifs = 0
        laverie_energie = 0
        
        # Calculer par type et unité
        for consumption in consumptions:
            resource_type = consumption['resource_type']
            quantity = consumption['quantity']
            step_name = consumption.get('step_name')
            
            if step_name == ProcessStepEnum.CONCASSAGE.value:
                if resource_type == ResourceType.ENERGY.value:
                    concassage_energie += quantity
            elif step_name == ProcessStepEnum.LAVERIE.value:
                if resource_type == ResourceType.WATER.value:
                    laverie_eau += quantity
                elif resource_type == ResourceType.REACTIVE.value:
                    laverie_reactifs += quantity
                elif resource_type == ResourceType.ENERGY.value:
                    laverie_energie += quantity
        
        # Coûts estimés (à configurer selon les tarifs)
        tarif_energie = 0.9  # DH/kWh
        tarif_eau = 3.0      # DH/m³
        tarif_reactifs = 18.0 # DH/kg
        tarif_carburant = 9.0 # DH/L
        
        return {
            'concassage_energie': f"{concassage_energie:.0f}",
            'cout_energie_concassage': f"{concassage_energie * tarif_energie:.0f}",
            'concassage_carburant': "85",  # Valeur par défaut
            'cout_carburant_concassage': f"{85 * tarif_carburant:.0f}",
            
            'laverie_eau': f"{laverie_eau:.0f}",
            'cout_eau': f"{laverie_eau * tarif_eau:.0f}",
            'laverie_reactifs': f"{laverie_reactifs:.0f}",
            'cout_reactifs': f"{laverie_reactifs * tarif_reactifs:.0f}",
            'laverie_energie': f"{laverie_energie:.0f}",
            'cout_energie_laverie': f"{laverie_energie * tarif_energie:.0f}",
        }
        
    def _get_donnees_arrets(self, start_datetime: datetime, end_datetime: datetime) -> Dict[str, Any]:
        """Récupère les données d'arrêts."""
        from models.enums import ProcessStepEnum
        
        # Arrêts concassage
        crushing_downtimes = self.db_manager.get_downtime_summary(
            step=ProcessStepEnum.CONCASSAGE,
            start_date=start_datetime,
            end_date=end_datetime
        )
        
        # Arrêts laverie
        washing_downtimes = self.db_manager.get_downtime_summary(
            step=ProcessStepEnum.LAVERIE,
            start_date=start_datetime,
            end_date=end_datetime
        )
        
        # Calculs
        nb_arrets_concassage = len(crushing_downtimes)
        duree_arrets_concassage = sum(d['duration'] for d in crushing_downtimes)
        
        nb_arrets_laverie = len(washing_downtimes)
        duree_arrets_laverie = sum(d['duration'] for d in washing_downtimes)
        
        nb_arrets_total = nb_arrets_concassage + nb_arrets_laverie
        duree_arrets_total = duree_arrets_concassage + duree_arrets_laverie
        
        # Objectif arrêts
        objectif_arret = 120  # minutes
        ecart_arret = duree_arrets_total - objectif_arret
        statut_arret = "✅ Bon" if duree_arrets_total <= objectif_arret else "⚠️ Élevé"
        
        # Détail des arrêts (format tableau)
        detail_arrets = ""
        all_downtimes = []
        
        for d in crushing_downtimes:
            d['unit'] = 'Concassage'
            all_downtimes.append(d)
        for d in washing_downtimes:
            d['unit'] = 'Laverie'
            all_downtimes.append(d)
            
        # Trier par heure
        all_downtimes.sort(key=lambda x: x['downtime_date'])
        
        for downtime in all_downtimes:
            heure = downtime['downtime_date'].strftime("%H:%M")
            unite = downtime['unit']
            duree = f"{downtime['duration']:.0f} min"
            raison = downtime['reason']
            description = downtime.get('description', 'N/A')
            
            detail_arrets += f"| {heure} | {unite} | {duree} | {raison} | {description} |\n"
        
        return {
            'nb_arrets_concassage': str(nb_arrets_concassage),
            'duree_arrets_concassage': f"{duree_arrets_concassage:.0f}",
            'nb_arrets_laverie': str(nb_arrets_laverie),
            'duree_arrets_laverie': f"{duree_arrets_laverie:.0f}",
            'nb_arrets_total': str(nb_arrets_total),
            'duree_arrets_total': f"{duree_arrets_total:.0f}",
            'objectif_arret': str(objectif_arret),
            'arret_realise': f"{duree_arrets_total:.0f}",
            'ecart_arret': f"{ecart_arret:+.0f}",
            'statut_arret': statut_arret,
            'detail_arrets': detail_arrets,
            'impact_concassage': "8.5",  # Calcul approximatif
            'impact_laverie': "6.2",
            'impact_total': "14.7"
        }
        
    def _calculer_qualite_moyenne(self, productions: List[Dict]) -> float:
        """Calcule la qualité moyenne pondérée."""
        total_weighted_quality = 0
        total_quantity = 0
        
        for p in productions:
            if p.get('quality') and p['quantity'] > 0:
                try:
                    if isinstance(p['quality'], str) and '%' in p['quality']:
                        quality_val = float(p['quality'].replace('%', '').strip())
                    else:
                        quality_val = float(p['quality'])
                    total_weighted_quality += quality_val * p['quantity']
                    total_quantity += p['quantity']
                except (ValueError, TypeError):
                    pass
        
        return total_weighted_quality / total_quantity if total_quantity > 0 else 0
        
    def _get_analyses_tendances(self, date_production: date) -> Dict[str, Any]:
        """Récupère les analyses et tendances."""
        # Données d'hier
        hier = date_production - timedelta(days=1)
        
        # Pour simplifier, on utilise des valeurs par défaut
        # Dans une vraie implémentation, on récupérerait les données d'hier
        
        return {
            'prod_aujourd_hui': "487.5",
            'prod_hier': "465.2",
            'evolution_prod': "+22.3 T",
            'tendance_prod': "📈 +4.8%",
            
            'qual_aujourd_hui': "31.2",
            'qual_hier': "30.8",
            'evolution_qual': "+0.4%",
            'tendance_qual': "📈 +1.3%",
            
            'eff_aujourd_hui': "88.3",
            'eff_hier': "86.1",
            'evolution_eff': "+2.2%",
            'tendance_eff': "📈 +2.6%",
            
            'arret_aujourd_hui': "95",
            'arret_hier': "125",
            'evolution_arret': "-30 min",
            'tendance_arret': "📈 -24.0%",
            
            'historique_7_jours': self._generer_historique_7_jours(date_production)
        }
        
    def _generer_historique_7_jours(self, date_production: date) -> str:
        """Génère l'historique des 7 derniers jours."""
        historique = ""
        jours_fr = ["Lun", "Mar", "Mer", "Jeu", "Ven", "Sam", "Dim"]
        
        # Données simulées pour l'exemple
        donnees_7j = [
            (445.2, 30.1, 84.5, 135),
            (478.9, 30.9, 87.2, 110),
            (456.7, 29.8, 83.1, 145),
            (492.1, 31.5, 89.1, 85),
            (488.3, 30.7, 88.8, 95),
            (465.2, 30.8, 86.1, 125),
            (487.5, 31.2, 88.3, 95)
        ]
        
        for i, (prod, qual, eff, arret) in enumerate(donnees_7j):
            jour_date = date_production - timedelta(days=6-i)
            jour_nom = jours_fr[jour_date.weekday()]
            date_str = f"{jour_nom} {jour_date.strftime('%d/%m')}"
            
            historique += f"| {date_str} | {prod} | {qual} | {eff} | {arret} |\n"
            
        return historique
        
    def _get_calculs_economiques(self, donnees: Dict[str, Any]) -> Dict[str, Any]:
        """Calcule les indicateurs économiques."""
        # Récupérer les coûts depuis les données
        cout_energie_concassage = float(donnees.get('cout_energie_concassage', '0'))
        cout_carburant_concassage = float(donnees.get('cout_carburant_concassage', '0'))
        cout_eau = float(donnees.get('cout_eau', '0'))
        cout_reactifs = float(donnees.get('cout_reactifs', '0'))
        cout_energie_laverie = float(donnees.get('cout_energie_laverie', '0'))
        
        # Coûts additionnels
        cout_matiere = 24000  # DH (à calculer selon quantité et prix)
        cout_main_oeuvre = 540  # DH (équipe de 12h)
        
        cout_energie_total = cout_energie_concassage + cout_energie_laverie
        cout_total = (cout_matiere + cout_energie_total + cout_eau + 
                     cout_reactifs + cout_main_oeuvre)
        
        # Pourcentages
        pct_matiere = (cout_matiere / cout_total * 100)
        pct_energie = (cout_energie_total / cout_total * 100)
        pct_eau = (cout_eau / cout_total * 100)
        pct_reactifs = (cout_reactifs / cout_total * 100)
        pct_main_oeuvre = (cout_main_oeuvre / cout_total * 100)
        
        # Indicateurs économiques
        production_realisee = float(donnees.get('production_realisee', '0'))
        cout_par_tonne = cout_total / production_realisee if production_realisee > 0 else 0
        
        # Prix de vente estimé (à configurer)
        prix_vente_tonne = 90  # DH/T
        chiffre_affaires = production_realisee * prix_vente_tonne
        marge_brute = chiffre_affaires - cout_total
        rentabilite = (marge_brute / chiffre_affaires * 100) if chiffre_affaires > 0 else 0
        
        return {
            'cout_matiere': f"{cout_matiere:,.0f}",
            'cout_energie_total': f"{cout_energie_total:,.0f}",
            'cout_total': f"{cout_total:,.0f}",
            'cout_main_oeuvre': f"{cout_main_oeuvre:,.0f}",
            
            'pct_matiere': f"{pct_matiere:.1f}",
            'pct_energie': f"{pct_energie:.1f}",
            'pct_eau': f"{pct_eau:.1f}",
            'pct_reactifs': f"{pct_reactifs:.1f}",
            'pct_main_oeuvre': f"{pct_main_oeuvre:.1f}",
            
            'cout_par_tonne': f"{cout_par_tonne:.2f}",
            'marge_brute': f"{marge_brute:,.0f}",
            'rentabilite': f"{rentabilite:.1f}"
        }
        
    def _get_donnees_qualite(self, start_datetime: datetime, end_datetime: datetime) -> Dict[str, Any]:
        """Récupère les données de contrôle qualité."""
        # Pour l'exemple, on utilise des données simulées
        # Dans une vraie implémentation, on récupérerait depuis une table qualité
        
        analyses_qualite = """| CONC-001 | 08:00 | 28.3 | 2.1% | 0-5mm: 85% | ✅ |
| CONC-002 | 12:00 | 28.7 | 1.9% | 0-5mm: 87% | ✅ |
| LAV-001 | 10:00 | 31.1 | 8.5% | 0-2mm: 92% | ✅ |
| LAV-002 | 14:00 | 31.3 | 8.2% | 0-2mm: 94% | ✅ |
| LAV-003 | 17:00 | 31.2 | 8.1% | 0-2mm: 93% | ✅ |"""
        
        return {
            'analyses_qualite': analyses_qualite,
            'p2o5_min': "28.3",
            'p2o5_max': "31.3",
            'p2o5_moy': "30.1",
            'p2o5_ecart': "1.2",
            'hum_min': "1.9",
            'hum_max': "8.5",
            'hum_moy': "5.8",
            'hum_ecart': "3.1"
        }
        
    def _charger_template(self) -> str:
        """Charge le template de rapport."""
        try:
            with open(self.template_path, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            # Template par défaut si fichier non trouvé
            return "# RAPPORT DE PRODUCTION\n\nDate: {date_production}\nProduction: {production_realisee} T"
            
    def _remplacer_variables(self, template: str, donnees: Dict[str, Any]) -> str:
        """Remplace les variables dans le template."""
        rapport = template
        
        for cle, valeur in donnees.items():
            placeholder = "{" + cle + "}"
            rapport = rapport.replace(placeholder, str(valeur))
            
        # Remplacer les variables non trouvées par des valeurs par défaut
        import re
        variables_manquantes = re.findall(r'\{([^}]+)\}', rapport)
        
        for var in variables_manquantes:
            placeholder = "{" + var + "}"
            rapport = rapport.replace(placeholder, "N/A")
            
        return rapport
        
    def sauvegarder_rapport(self, rapport: str, date_production: date, 
                           dossier: str = "rapports") -> str:
        """
        Sauvegarde le rapport dans un fichier.
        
        Args:
            rapport: Contenu du rapport
            date_production: Date du rapport
            dossier: Dossier de destination
            
        Returns:
            str: Chemin du fichier sauvegardé
        """
        # Créer le dossier s'il n'existe pas
        os.makedirs(dossier, exist_ok=True)
        
        # Nom du fichier
        nom_fichier = f"rapport_production_{date_production.strftime('%Y%m%d')}.md"
        chemin_fichier = os.path.join(dossier, nom_fichier)
        
        # Sauvegarder
        with open(chemin_fichier, 'w', encoding='utf-8') as f:
            f.write(rapport)
            
        return chemin_fichier


# Exemple d'utilisation
if __name__ == "__main__":
    """Exemple d'utilisation du générateur de rapports."""

    # Simuler un db_manager pour les tests
    class MockDBManager:
        def get_production_summary(self, step, start_date, end_date):
            # Données simulées
            if step.value == "CONCASSAGE":
                return [
                    {'quantity': 300.0, 'quantity_used': 400.0, 'production_hours': 6.0, 'quality': '28.5'},
                    {'quantity': 325.0, 'quantity_used': 400.0, 'production_hours': 5.5, 'quality': '28.7'}
                ]
            else:  # LAVERIE
                return [
                    {'quantity': 240.0, 'quantity_used': 290.0, 'production_hours': 5.5, 'quality': '31.1'},
                    {'quantity': 247.5, 'quantity_used': 290.0, 'production_hours': 5.3, 'quality': '31.3'}
                ]

        def get_resource_consumptions(self, start_date, end_date):
            from models.enums import ResourceType, ProcessStepEnum
            return [
                {'resource_type': ResourceType.ENERGY.value, 'quantity': 1250, 'step_name': ProcessStepEnum.CONCASSAGE.value},
                {'resource_type': ResourceType.WATER.value, 'quantity': 145, 'step_name': ProcessStepEnum.LAVERIE.value},
                {'resource_type': ResourceType.REACTIVE.value, 'quantity': 125, 'step_name': ProcessStepEnum.LAVERIE.value},
                {'resource_type': ResourceType.ENERGY.value, 'quantity': 980, 'step_name': ProcessStepEnum.LAVERIE.value}
            ]

        def get_downtime_summary(self, step, start_date, end_date):
            if step.value == "CONCASSAGE":
                return [
                    {'duration': 20, 'reason': 'Maintenance préventive', 'description': 'Graissage roulements', 'downtime_date': start_date.replace(hour=8, minute=15)},
                    {'duration': 25, 'reason': 'Changement équipe', 'description': 'Passation consignes', 'downtime_date': start_date.replace(hour=12, minute=0)}
                ]
            else:  # LAVERIE
                return [
                    {'duration': 15, 'reason': 'Nettoyage filtres', 'description': 'Remplacement filtres', 'downtime_date': start_date.replace(hour=10, minute=30)},
                    {'duration': 20, 'reason': 'Ajustement paramètres', 'description': 'Optimisation débit', 'downtime_date': start_date.replace(hour=14, minute=45)},
                    {'duration': 15, 'reason': 'Contrôle qualité', 'description': 'Prélèvement échantillons', 'downtime_date': start_date.replace(hour=16, minute=20)}
                ]

    # Test du générateur
    mock_db = MockDBManager()
    generator = RapportProductionGenerator(mock_db)

    # Générer rapport pour aujourd'hui
    from datetime import date
    rapport = generator.generer_rapport_journalier(date.today())

    # Sauvegarder
    chemin = generator.sauvegarder_rapport(rapport, date.today())

    print(f"Rapport généré et sauvegardé dans : {chemin}")
    print("\n" + "="*50)
    print("APERÇU DU RAPPORT :")
    print("="*50)
    print(rapport[:1000] + "..." if len(rapport) > 1000 else rapport)
