"""
Onglet des paramètres de l'application.
"""
# Standard library imports
import logging
import os
import shutil
from typing import Dict, Any, List, Optional

# Third-party imports
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QColor, QIcon, QFont
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                             QLabel, QComboBox, QPushButton, QTabWidget,
                             QScrollArea, QCheckBox, QListWidget, QListWidgetItem,
                             QDialog, QLineEdit, QFormLayout, QSpinBox, QMessageBox,
                             QColorDialog, QGridLayout, QFrame, QApplication,
                             QInputDialog)

# Local imports
from config.dashboard_config import DashboardConfig
from ui.theme_manager import theme_manager, apply_theme
from utils.cache_manager import get_cache_stats, clear_cache, clean_expired_cache

class ColorPreview(QFrame):
    """Widget pour prévisualiser une couleur."""
    
    def __init__(self, color: str, parent: Optional[QWidget] = None):
        """
        Initialise le widget de prévisualisation de couleur.
        
        Args:
            color: Couleur à prévisualiser (format hexadécimal)
            parent: Widget parent
        """
        super().__init__(parent)
        self.setFixedSize(24, 24)
        self.setFrameShape(QFrame.Box)
        self.setFrameShadow(QFrame.Plain)
        self.setLineWidth(1)
        self.set_color(color)
    
    def set_color(self, color: str) -> None:
        """
        Définit la couleur à prévisualiser.
        
        Args:
            color: Couleur à prévisualiser (format hexadécimal)
        """
        self.setStyleSheet(f"background-color: {color}; border: 1px solid #cccccc;")

class ThemeEditorDialog(QDialog):
    """Boîte de dialogue pour éditer un thème."""
    
    def __init__(self, theme_id: str, theme_data: Dict[str, Any], parent: Optional[QWidget] = None):
        """
        Initialise la boîte de dialogue d'édition de thème.
        
        Args:
            theme_id: ID du thème à éditer
            theme_data: Données du thème
            parent: Widget parent
        """
        super().__init__(parent)
        self.theme_id = theme_id
        self.theme_data = theme_data.copy()
        self.is_new_theme = theme_id not in theme_manager.get_all_themes()
        
        self.init_ui()
    
    def init_ui(self) -> None:
        """Initialise l'interface utilisateur."""
        self.setWindowTitle("Éditeur de thème")
        self.setMinimumSize(600, 400)
        
        layout = QVBoxLayout(self)
        
        # Informations générales
        form_layout = QFormLayout()
        
        self.name_edit = QLineEdit(self.theme_data.get("name", ""))
        form_layout.addRow("Nom:", self.name_edit)
        
        self.description_edit = QLineEdit(self.theme_data.get("description", ""))
        form_layout.addRow("Description:", self.description_edit)
        
        layout.addLayout(form_layout)
        
        # Couleurs du thème
        colors_group = QGroupBox("Couleurs du thème")
        colors_layout = QGridLayout()
        
        self.color_widgets = {}
        styles = self.theme_data.get("styles", {})
        
        row = 0
        col = 0
        for color_key, color_value in styles.items():
            # Créer un label pour le nom de la couleur
            label = QLabel(color_key.replace("_", " ").title())
            
            # Créer un widget de prévisualisation de la couleur
            color_preview = ColorPreview(color_value)
            
            # Créer un bouton pour changer la couleur
            color_button = QPushButton("Changer")
            color_button.setProperty("color_key", color_key)
            color_button.clicked.connect(self.on_color_button_clicked)
            
            # Ajouter les widgets à la grille
            colors_layout.addWidget(label, row, col)
            colors_layout.addWidget(color_preview, row, col + 1)
            colors_layout.addWidget(color_button, row, col + 2)
            
            # Stocker les widgets pour pouvoir les mettre à jour
            self.color_widgets[color_key] = {
                "label": label,
                "preview": color_preview,
                "button": color_button
            }
            
            # Passer à la ligne suivante
            row += 1
            if row >= 10:  # 10 couleurs par colonne
                row = 0
                col += 3
        
        colors_group.setLayout(colors_layout)
        
        # Ajouter un scroll area pour les couleurs
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(colors_group)
        layout.addWidget(scroll_area)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("Enregistrer")
        self.save_button.clicked.connect(self.accept)
        buttons_layout.addWidget(self.save_button)
        
        self.cancel_button = QPushButton("Annuler")
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)
        
        layout.addLayout(buttons_layout)
    
    def on_color_button_clicked(self) -> None:
        """Gère le clic sur un bouton de couleur."""
        button = self.sender()
        if not button:
            return
        
        color_key = button.property("color_key")
        current_color = self.theme_data["styles"][color_key]
        
        color_dialog = QColorDialog(QColor(current_color), self)
        if color_dialog.exec_():
            new_color = color_dialog.selectedColor().name()
            self.theme_data["styles"][color_key] = new_color
            self.color_widgets[color_key]["preview"].set_color(new_color)
    
    def get_theme_data(self) -> Dict[str, Any]:
        """
        Récupère les données du thème modifié.
        
        Returns:
            Dictionnaire contenant les données du thème
        """
        self.theme_data["name"] = self.name_edit.text()
        self.theme_data["description"] = self.description_edit.text()
        return self.theme_data

class DashboardEditorDialog(QDialog):
    """Boîte de dialogue pour éditer un tableau de bord."""
    
    def __init__(self, dashboard_id: str, dashboard_data: Dict[str, Any], parent: Optional[QWidget] = None):
        """
        Initialise la boîte de dialogue d'édition de tableau de bord.
        
        Args:
            dashboard_id: ID du tableau de bord à éditer
            dashboard_data: Données du tableau de bord
            parent: Widget parent
        """
        super().__init__(parent)
        self.dashboard_id = dashboard_id
        self.dashboard_data = dashboard_data.copy()
        self.dashboard_config = DashboardConfig()
        
        self.init_ui()
    
    def init_ui(self) -> None:
        """Initialise l'interface utilisateur."""
        self.setWindowTitle("Éditeur de tableau de bord")
        self.setMinimumSize(600, 400)
        
        layout = QVBoxLayout(self)
        
        # Informations générales
        form_layout = QFormLayout()
        
        self.name_edit = QLineEdit(self.dashboard_data.get("name", ""))
        form_layout.addRow("Nom:", self.name_edit)
        
        layout.addLayout(form_layout)
        
        # Liste des widgets
        widgets_group = QGroupBox("Widgets")
        widgets_layout = QVBoxLayout()
        
        self.widgets_list = QListWidget()
        self.update_widgets_list()
        widgets_layout.addWidget(self.widgets_list)
        
        # Boutons pour gérer les widgets
        widgets_buttons_layout = QHBoxLayout()
        
        self.add_widget_button = QPushButton("Ajouter")
        self.add_widget_button.clicked.connect(self.on_add_widget)
        widgets_buttons_layout.addWidget(self.add_widget_button)
        
        self.edit_widget_button = QPushButton("Modifier")
        self.edit_widget_button.clicked.connect(self.on_edit_widget)
        widgets_buttons_layout.addWidget(self.edit_widget_button)
        
        self.delete_widget_button = QPushButton("Supprimer")
        self.delete_widget_button.clicked.connect(self.on_delete_widget)
        widgets_buttons_layout.addWidget(self.delete_widget_button)
        
        widgets_layout.addLayout(widgets_buttons_layout)
        widgets_group.setLayout(widgets_layout)
        layout.addWidget(widgets_group)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("Enregistrer")
        self.save_button.clicked.connect(self.accept)
        buttons_layout.addWidget(self.save_button)
        
        self.cancel_button = QPushButton("Annuler")
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)
        
        layout.addLayout(buttons_layout)
    
    def update_widgets_list(self) -> None:
        """Met à jour la liste des widgets."""
        self.widgets_list.clear()
        
        for widget in self.dashboard_data.get("widgets", []):
            item = QListWidgetItem(f"{widget.get('title', 'Sans titre')} ({widget.get('type', 'inconnu')})")
            item.setData(Qt.UserRole, widget)
            self.widgets_list.addItem(item)
    
    def on_add_widget(self) -> None:
        """Gère l'ajout d'un widget."""
        # TODO: Implémenter l'ajout de widget
        QMessageBox.information(self, "Information", "Fonctionnalité en cours de développement.")
    
    def on_edit_widget(self) -> None:
        """Gère la modification d'un widget."""
        # TODO: Implémenter la modification de widget
        QMessageBox.information(self, "Information", "Fonctionnalité en cours de développement.")
    
    def on_delete_widget(self) -> None:
        """Gère la suppression d'un widget."""
        current_item = self.widgets_list.currentItem()
        if not current_item:
            return
        
        widget_index = self.widgets_list.row(current_item)
        
        reply = QMessageBox.question(
            self, "Confirmation",
            "Voulez-vous vraiment supprimer ce widget ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            del self.dashboard_data["widgets"][widget_index]
            self.update_widgets_list()
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """
        Récupère les données du tableau de bord modifié.
        
        Returns:
            Dictionnaire contenant les données du tableau de bord
        """
        self.dashboard_data["name"] = self.name_edit.text()
        return self.dashboard_data

class SettingsTab(QWidget):
    """Onglet des paramètres de l'application."""
    
    def __init__(self, parent: Optional[QWidget] = None, db_manager=None):
        """
        Initialise l'onglet des paramètres.
        
        Args:
            parent: Widget parent
            db_manager: Gestionnaire de base de données
        """
        super().__init__(parent)
        self.theme_manager = theme_manager
        self.dashboard_config = DashboardConfig()
        self.db_manager = db_manager
        self.init_ui()
    
    def init_ui(self) -> None:
        """Initialise l'interface utilisateur avec un design moderne et professionnel."""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # En-tête principal
        header_frame = QFrame()
        header_frame.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E3A8A, stop:1 #3B82F6);
                border-radius: 12px;
                padding: 15px;
                margin-bottom: 10px;
            }}
        """)
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 15, 20, 15)

        title_label = QLabel("⚙️ PARAMÈTRES SOTRAMINE")
        title_label.setStyleSheet("""
            color: white;
            font-size: 20px;
            font-weight: bold;
            margin: 0;
        """)
        title_label.setAlignment(Qt.AlignCenter)

        subtitle_label = QLabel("Configuration et personnalisation de l'application")
        subtitle_label.setStyleSheet("""
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            margin: 5px 0 0 0;
        """)
        subtitle_label.setAlignment(Qt.AlignCenter)

        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        main_layout.addWidget(header_frame)

        # Onglets de paramètres avec style moderne
        self.settings_tabs = QTabWidget()
        self.settings_tabs.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 2px solid #E5E7EB;
                border-radius: 8px;
                background-color: white;
                padding: 10px;
            }}
            QTabBar::tab {{
                background-color: #F3F4F6;
                border: 1px solid #D1D5DB;
                padding: 12px 20px;
                margin-right: 2px;
                border-radius: 6px 6px 0 0;
                font-weight: bold;
                font-size: 12px;
            }}
            QTabBar::tab:selected {{
                background-color: white;
                border-bottom-color: white;
                color: #1E3A8A;
            }}
            QTabBar::tab:hover {{
                background-color: #E5E7EB;
            }}
        """)

        # === ONGLET APPARENCE ===
        appearance_tab = QWidget()
        appearance_layout = QVBoxLayout(appearance_tab)
        appearance_layout.setSpacing(15)
        appearance_layout.setContentsMargins(15, 15, 15, 15)

        # Groupe thèmes avec design moderne
        themes_group = QGroupBox("🎨 Thèmes et Apparence")
        themes_group.setFont(QFont("Segoe UI", 11, QFont.Bold))
        themes_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 14px;
                font-weight: bold;
                color: #1F2937;
                border: 2px solid #E5E7EB;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #F9FAFB;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px;
                background-color: white;
                border-radius: 4px;
            }}
        """)
        themes_layout = QVBoxLayout()
        themes_layout.setSpacing(12)
        themes_layout.setContentsMargins(15, 20, 15, 15)

        # Sélection du thème avec style moderne
        theme_selection_frame = QFrame()
        theme_selection_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 8px;
                padding: 12px;
            }
        """)
        theme_layout = QHBoxLayout(theme_selection_frame)
        theme_layout.setContentsMargins(10, 8, 10, 8)

        theme_label = QLabel("Thème actuel :")
        theme_label.setStyleSheet("font-weight: bold; color: #374151; font-size: 13px;")
        theme_layout.addWidget(theme_label)

        self.theme_combo = QComboBox()
        self.theme_combo.setMinimumWidth(200)
        self.theme_combo.setStyleSheet(f"""
            QComboBox {{
                padding: 8px 12px;
                border: 2px solid #D1D5DB;
                border-radius: 6px;
                background-color: white;
                font-size: 12px;
                font-weight: bold;
            }}
            QComboBox:focus {{
                border-color: #3B82F6;
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
        """)
        self.update_theme_combo()
        self.theme_combo.currentIndexChanged.connect(self.on_theme_changed)
        theme_layout.addWidget(self.theme_combo)
        theme_layout.addStretch()

        themes_layout.addWidget(theme_selection_frame)

        # Boutons de gestion des thèmes
        theme_buttons_frame = QFrame()
        theme_buttons_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        theme_buttons_layout = QHBoxLayout(theme_buttons_frame)
        theme_buttons_layout.setContentsMargins(8, 5, 8, 5)

        self.new_theme_button = QPushButton("➕ Nouveau Thème")
        self.edit_theme_button = QPushButton("✏️ Modifier")
        self.delete_theme_button = QPushButton("🗑️ Supprimer")

        for btn in [self.new_theme_button, self.edit_theme_button, self.delete_theme_button]:
            btn.setStyleSheet(f"""
                QPushButton {{
                    padding: 8px 15px;
                    border: 2px solid #D1D5DB;
                    border-radius: 6px;
                    background-color: white;
                    font-weight: bold;
                    font-size: 11px;
                    color: #374151;
                }}
                QPushButton:hover {{
                    background-color: #F3F4F6;
                    border-color: #3B82F6;
                    color: #1E3A8A;
                }}
                QPushButton:pressed {{
                    background-color: #E5E7EB;
                }}
            """)

        self.new_theme_button.clicked.connect(self.on_new_theme)
        self.edit_theme_button.clicked.connect(self.on_edit_theme)
        self.delete_theme_button.clicked.connect(self.on_delete_theme)

        theme_buttons_layout.addWidget(self.new_theme_button)
        theme_buttons_layout.addWidget(self.edit_theme_button)
        theme_buttons_layout.addWidget(self.delete_theme_button)
        theme_buttons_layout.addStretch()

        themes_layout.addWidget(theme_buttons_frame)
        themes_group.setLayout(themes_layout)
        appearance_layout.addWidget(themes_group)
        
        # Groupe options d'interface avec design moderne
        ui_options_group = QGroupBox("🖥️ Options d'Interface")
        ui_options_group.setFont(QFont("Segoe UI", 11, QFont.Bold))
        ui_options_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 14px;
                font-weight: bold;
                color: #1F2937;
                border: 2px solid #E5E7EB;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #F9FAFB;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px;
                background-color: white;
                border-radius: 4px;
            }}
        """)
        ui_options_layout = QVBoxLayout()
        ui_options_layout.setSpacing(10)
        ui_options_layout.setContentsMargins(15, 20, 15, 15)

        # Options avec checkboxes stylées
        options_frame = QFrame()
        options_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        options_layout = QVBoxLayout(options_frame)
        options_layout.setSpacing(12)

        # Style pour les checkboxes
        checkbox_style = """
            QCheckBox {
                font-size: 12px;
                font-weight: bold;
                color: #374151;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #D1D5DB;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #3B82F6;
                border-color: #3B82F6;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xMC42IDEuNEw0LjIgNy44TDEuNCA1IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
            QCheckBox::indicator:hover {
                border-color: #3B82F6;
            }
        """

        # Interface responsive
        self.responsive_checkbox = QCheckBox("🔄 Interface responsive (s'adapte à la taille de l'écran)")
        self.responsive_checkbox.setChecked(True)
        self.responsive_checkbox.setStyleSheet(checkbox_style)
        options_layout.addWidget(self.responsive_checkbox)

        # Animations
        self.animations_checkbox = QCheckBox("✨ Activer les animations et transitions")
        self.animations_checkbox.setChecked(True)
        self.animations_checkbox.setStyleSheet(checkbox_style)
        options_layout.addWidget(self.animations_checkbox)

        # Tooltips
        self.tooltips_checkbox = QCheckBox("💬 Afficher les infobulles d'aide")
        self.tooltips_checkbox.setChecked(True)
        self.tooltips_checkbox.setStyleSheet(checkbox_style)
        options_layout.addWidget(self.tooltips_checkbox)

        # Mode sombre (nouveau)
        self.dark_mode_checkbox = QCheckBox("🌙 Mode sombre automatique")
        self.dark_mode_checkbox.setChecked(False)
        self.dark_mode_checkbox.setStyleSheet(checkbox_style)
        options_layout.addWidget(self.dark_mode_checkbox)

        # Notifications
        self.notifications_checkbox = QCheckBox("🔔 Notifications système")
        self.notifications_checkbox.setChecked(True)
        self.notifications_checkbox.setStyleSheet(checkbox_style)
        options_layout.addWidget(self.notifications_checkbox)

        ui_options_layout.addWidget(options_frame)
        ui_options_group.setLayout(ui_options_layout)
        appearance_layout.addWidget(ui_options_group)

        appearance_layout.addStretch()
        self.settings_tabs.addTab(appearance_tab, "🎨 Apparence")
        
        # Onglet "Tableaux de bord" supprimé - fonctionnalité non nécessaire
        
        # === ONGLET PERFORMANCE ===
        performance_tab = QWidget()
        performance_layout = QVBoxLayout(performance_tab)
        performance_layout.setSpacing(15)
        performance_layout.setContentsMargins(15, 15, 15, 15)

        # Groupe cache avec design moderne
        cache_group = QGroupBox("💾 Cache de Données")
        cache_group.setFont(QFont("Segoe UI", 11, QFont.Bold))
        cache_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 14px;
                font-weight: bold;
                color: #1F2937;
                border: 2px solid #E5E7EB;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #F9FAFB;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px;
                background-color: white;
                border-radius: 4px;
            }}
        """)
        cache_layout = QVBoxLayout()
        cache_layout.setSpacing(12)
        cache_layout.setContentsMargins(15, 20, 15, 15)

        # Statistiques du cache avec cartes modernes
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        stats_layout = QGridLayout(stats_frame)
        stats_layout.setSpacing(15)

        # Créer des cartes de statistiques
        def create_stat_card(title, value_widget, icon, color):
            card = QFrame()
            card.setStyleSheet(f"""
                QFrame {{
                    background-color: {color}15;
                    border: 2px solid {color}30;
                    border-radius: 8px;
                    padding: 12px;
                }}
            """)
            card_layout = QVBoxLayout(card)
            card_layout.setSpacing(5)

            # En-tête avec icône
            header_layout = QHBoxLayout()
            icon_label = QLabel(icon)
            icon_label.setStyleSheet(f"font-size: 16px; color: {color};")
            title_label = QLabel(title)
            title_label.setStyleSheet(f"font-weight: bold; color: {color}; font-size: 11px;")

            header_layout.addWidget(icon_label)
            header_layout.addWidget(title_label)
            header_layout.addStretch()

            # Valeur
            value_widget.setStyleSheet(f"font-size: 18px; font-weight: bold; color: {color}; margin: 5px 0;")
            value_widget.setAlignment(Qt.AlignCenter)

            card_layout.addLayout(header_layout)
            card_layout.addWidget(value_widget)
            return card

        self.cache_entries_label = QLabel("0")
        self.cache_hits_label = QLabel("0")
        self.cache_misses_label = QLabel("0")
        self.cache_hit_ratio_label = QLabel("0%")

        # Ajouter les cartes de statistiques
        stats_layout.addWidget(create_stat_card("Entrées", self.cache_entries_label, "📊", "#3B82F6"), 0, 0)
        stats_layout.addWidget(create_stat_card("Hits", self.cache_hits_label, "✅", "#10B981"), 0, 1)
        stats_layout.addWidget(create_stat_card("Misses", self.cache_misses_label, "❌", "#EF4444"), 1, 0)
        stats_layout.addWidget(create_stat_card("Ratio", self.cache_hit_ratio_label, "📈", "#8B5CF6"), 1, 1)

        cache_layout.addWidget(stats_frame)

        # Boutons de gestion du cache
        cache_buttons_frame = QFrame()
        cache_buttons_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 8px;
                padding: 12px;
            }
        """)
        cache_buttons_layout = QHBoxLayout(cache_buttons_frame)
        cache_buttons_layout.setSpacing(10)

        button_style = """
            QPushButton {
                padding: 10px 15px;
                border: 2px solid #D1D5DB;
                border-radius: 6px;
                background-color: white;
                font-weight: bold;
                font-size: 11px;
                color: #374151;
            }
            QPushButton:hover {
                background-color: #F3F4F6;
                border-color: #3B82F6;
                color: #1E3A8A;
            }
            QPushButton:pressed {
                background-color: #E5E7EB;
            }
        """

        self.clear_cache_button = QPushButton("🗑️ Vider le Cache")
        self.clear_cache_button.setStyleSheet(button_style)
        self.clear_cache_button.clicked.connect(self.on_clear_cache)

        self.clean_cache_button = QPushButton("🧹 Nettoyer Expiré")
        self.clean_cache_button.setStyleSheet(button_style)
        self.clean_cache_button.clicked.connect(self.on_clean_cache)

        self.refresh_cache_stats_button = QPushButton("🔄 Actualiser")
        self.refresh_cache_stats_button.setStyleSheet(button_style)
        self.refresh_cache_stats_button.clicked.connect(self.update_cache_stats)

        cache_buttons_layout.addWidget(self.clear_cache_button)
        cache_buttons_layout.addWidget(self.clean_cache_button)
        cache_buttons_layout.addWidget(self.refresh_cache_stats_button)
        cache_buttons_layout.addStretch()

        cache_layout.addWidget(cache_buttons_frame)
        cache_group.setLayout(cache_layout)
        performance_layout.addWidget(cache_group)
        
        # Groupe options de performance
        performance_options_group = QGroupBox("⚡ Options de Performance")
        performance_options_group.setFont(QFont("Segoe UI", 11, QFont.Bold))
        performance_options_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 14px;
                font-weight: bold;
                color: #1F2937;
                border: 2px solid #E5E7EB;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #F9FAFB;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px;
                background-color: white;
                border-radius: 4px;
            }}
        """)
        performance_options_layout = QVBoxLayout()
        performance_options_layout.setSpacing(12)
        performance_options_layout.setContentsMargins(15, 20, 15, 15)

        # Options frame
        options_frame = QFrame()
        options_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        options_layout = QVBoxLayout(options_frame)
        options_layout.setSpacing(15)

        # Cache activé
        self.cache_enabled_checkbox = QCheckBox("💾 Activer le cache de données")
        self.cache_enabled_checkbox.setChecked(True)
        self.cache_enabled_checkbox.setStyleSheet(checkbox_style)
        options_layout.addWidget(self.cache_enabled_checkbox)

        # Durée de vie du cache avec style moderne
        cache_ttl_frame = QFrame()
        cache_ttl_frame.setStyleSheet("""
            QFrame {
                background-color: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        cache_ttl_layout = QHBoxLayout(cache_ttl_frame)

        ttl_label = QLabel("⏱️ Durée de vie du cache :")
        ttl_label.setStyleSheet("font-weight: bold; color: #374151; font-size: 12px;")
        cache_ttl_layout.addWidget(ttl_label)

        self.cache_ttl_spinbox = QSpinBox()
        self.cache_ttl_spinbox.setMinimum(1)
        self.cache_ttl_spinbox.setMaximum(3600)
        self.cache_ttl_spinbox.setValue(300)
        self.cache_ttl_spinbox.setSuffix(" secondes")
        self.cache_ttl_spinbox.setStyleSheet("""
            QSpinBox {
                padding: 6px 10px;
                border: 2px solid #D1D5DB;
                border-radius: 4px;
                background-color: white;
                font-weight: bold;
                font-size: 11px;
            }
            QSpinBox:focus {
                border-color: #3B82F6;
            }
        """)
        cache_ttl_layout.addWidget(self.cache_ttl_spinbox)
        cache_ttl_layout.addStretch()

        options_layout.addWidget(cache_ttl_frame)

        # Auto-refresh
        self.auto_refresh_checkbox = QCheckBox("🔄 Actualisation automatique des données")
        self.auto_refresh_checkbox.setChecked(True)
        self.auto_refresh_checkbox.setStyleSheet(checkbox_style)
        options_layout.addWidget(self.auto_refresh_checkbox)

        performance_options_layout.addWidget(options_frame)
        performance_options_group.setLayout(performance_options_layout)
        performance_layout.addWidget(performance_options_group)

        performance_layout.addStretch()
        self.settings_tabs.addTab(performance_tab, "⚡ Performance")
        
        # === ONGLET SYSTÈME ===
        system_tab = QWidget()
        system_layout = QVBoxLayout(system_tab)
        system_layout.setSpacing(15)
        system_layout.setContentsMargins(15, 15, 15, 15)

        # Informations système
        system_info_group = QGroupBox("💻 Informations Système")
        system_info_group.setFont(QFont("Segoe UI", 11, QFont.Bold))
        system_info_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 14px;
                font-weight: bold;
                color: #1F2937;
                border: 2px solid #E5E7EB;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #F9FAFB;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px;
                background-color: white;
                border-radius: 4px;
            }}
        """)
        system_info_layout = QVBoxLayout()
        system_info_layout.setSpacing(10)
        system_info_layout.setContentsMargins(15, 20, 15, 15)

        # Informations dans un frame
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        info_layout = QVBoxLayout(info_frame)
        info_layout.setSpacing(8)

        import platform
        import sys
        from PyQt5.QtCore import QT_VERSION_STR

        system_infos = [
            ("🖥️ Système d'exploitation", f"{platform.system()} {platform.release()}"),
            ("🐍 Version Python", f"{sys.version.split()[0]}"),
            ("🎨 Version PyQt5", QT_VERSION_STR),
            ("📱 Application", "SOTRAMINE PHOSPHATE v1.0"),
            ("🏢 Entreprise", "SOTRAMINE"),
        ]

        for label, value in system_infos:
            info_row = QHBoxLayout()
            label_widget = QLabel(label)
            label_widget.setStyleSheet("font-weight: bold; color: #374151; font-size: 12px;")
            value_widget = QLabel(value)
            value_widget.setStyleSheet("color: #6B7280; font-size: 12px;")

            info_row.addWidget(label_widget)
            info_row.addStretch()
            info_row.addWidget(value_widget)
            info_layout.addLayout(info_row)

        system_info_layout.addWidget(info_frame)
        system_info_group.setLayout(system_info_layout)
        system_layout.addWidget(system_info_group)

        system_layout.addStretch()
        self.settings_tabs.addTab(system_tab, "💻 Système")

        # === ONGLET MAINTENANCE ===
        maintenance_tab = QWidget()
        maintenance_layout = QVBoxLayout(maintenance_tab)
        maintenance_layout.setSpacing(15)
        maintenance_layout.setContentsMargins(15, 15, 15, 15)

        # Groupe base de données avec design moderne
        database_group = QGroupBox("🗄️ Base de Données")
        database_group.setFont(QFont("Segoe UI", 11, QFont.Bold))
        database_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 14px;
                font-weight: bold;
                color: #1F2937;
                border: 2px solid #FEE2E2;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: #FEF2F2;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px;
                background-color: white;
                border-radius: 4px;
                color: #DC2626;
            }}
        """)
        database_layout = QVBoxLayout()
        database_layout.setSpacing(12)
        database_layout.setContentsMargins(15, 20, 15, 15)

        # Avertissement en haut
        warning_frame = QFrame()
        warning_frame.setStyleSheet("""
            QFrame {
                background-color: #FEF2F2;
                border: 2px solid #FECACA;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        warning_layout = QVBoxLayout(warning_frame)

        warning_icon = QLabel("⚠️")
        warning_icon.setAlignment(Qt.AlignCenter)
        warning_icon.setStyleSheet("font-size: 24px; margin-bottom: 5px;")

        warning_title = QLabel("ZONE DANGEREUSE")
        warning_title.setAlignment(Qt.AlignCenter)
        warning_title.setStyleSheet("font-size: 14px; font-weight: bold; color: #DC2626; margin-bottom: 5px;")

        warning_text = QLabel("La réinitialisation de la base de données supprimera définitivement toutes les données existantes. Cette action est irréversible.")
        warning_text.setWordWrap(True)
        warning_text.setAlignment(Qt.AlignCenter)
        warning_text.setStyleSheet("color: #7F1D1D; font-size: 11px; line-height: 1.4;")

        warning_layout.addWidget(warning_icon)
        warning_layout.addWidget(warning_title)
        warning_layout.addWidget(warning_text)

        database_layout.addWidget(warning_frame)

        # Bouton de réinitialisation
        reset_button_frame = QFrame()
        reset_button_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #FECACA;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        reset_button_layout = QHBoxLayout(reset_button_frame)

        reset_db_button = QPushButton("🗑️ RÉINITIALISER LA BASE DE DONNÉES")
        reset_db_button.setStyleSheet("""
            QPushButton {
                background-color: #DC2626;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #B91C1C;
            }
            QPushButton:pressed {
                background-color: #991B1B;
            }
        """)
        reset_db_button.clicked.connect(self.on_reset_database)

        reset_button_layout.addStretch()
        reset_button_layout.addWidget(reset_db_button)
        reset_button_layout.addStretch()

        database_layout.addWidget(reset_button_frame)
        database_group.setLayout(database_layout)
        maintenance_layout.addWidget(database_group)

        maintenance_layout.addStretch()
        self.settings_tabs.addTab(maintenance_tab, "🔧 Maintenance")
        
        main_layout.addWidget(self.settings_tabs)

        # Boutons principaux avec design moderne
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                padding: 15px;
                margin-top: 10px;
            }
        """)
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(12)

        # Bouton Appliquer
        self.apply_button = QPushButton("✅ Appliquer les Paramètres")
        self.apply_button.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #059669;
            }
            QPushButton:pressed {
                background-color: #047857;
            }
        """)
        self.apply_button.clicked.connect(self.on_apply)

        # Bouton Réinitialiser
        self.reset_button = QPushButton("🔄 Réinitialiser")
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
            QPushButton:pressed {
                background-color: #374151;
            }
        """)
        self.reset_button.clicked.connect(self.on_reset)

        # Bouton Aide (nouveau)
        self.help_button = QPushButton("❓ Aide")
        self.help_button.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
            QPushButton:pressed {
                background-color: #1D4ED8;
            }
        """)
        self.help_button.clicked.connect(self.on_help)

        buttons_layout.addStretch()
        buttons_layout.addWidget(self.help_button)
        buttons_layout.addWidget(self.reset_button)
        buttons_layout.addWidget(self.apply_button)

        main_layout.addWidget(buttons_frame)
        
        # Initialiser les statistiques du cache
        self.update_cache_stats()
    
    def update_theme_combo(self) -> None:
        """Met à jour la liste déroulante des thèmes."""
        self.theme_combo.clear()
        
        themes = self.theme_manager.get_all_themes()
        current_theme = self.theme_manager.current_theme
        
        for theme_id, theme_data in themes.items():
            self.theme_combo.addItem(theme_data["name"], theme_id)
            
            # Sélectionner le thème actuel
            if theme_id == current_theme:
                self.theme_combo.setCurrentIndex(self.theme_combo.count() - 1)
    
    # Méthodes des tableaux de bord supprimées - fonctionnalité non nécessaire
    
    def update_cache_stats(self) -> None:
        """Met à jour les statistiques du cache."""
        stats = get_cache_stats()
        
        self.cache_entries_label.setText(str(stats["entries"]))
        self.cache_hits_label.setText(str(stats["hits"]))
        self.cache_misses_label.setText(str(stats["misses"]))
        
        hit_ratio = stats["hit_ratio"] * 100
        self.cache_hit_ratio_label.setText(f"{hit_ratio:.2f}%")
    
    def on_theme_changed(self, index: int) -> None:
        """
        Gère le changement de thème.
        
        Args:
            index: Index du thème sélectionné
        """
        theme_id = self.theme_combo.itemData(index)
        if theme_id:
            self.theme_manager.set_theme(theme_id)
            
            # Appliquer le thème à l'application
            app = QApplication.instance()
            if app:
                apply_theme(app)
    
    def on_new_theme(self) -> None:
        """Gère la création d'un nouveau thème."""
        # Créer un nouveau thème basé sur le thème actuel
        current_theme = self.theme_manager.get_theme()
        new_theme_data = {
            "name": "Nouveau thème",
            "description": "Thème personnalisé",
            "styles": current_theme["styles"].copy()
        }
        
        # Ouvrir la boîte de dialogue d'édition de thème
        dialog = ThemeEditorDialog("new_theme", new_theme_data, self)
        if dialog.exec_():
            theme_data = dialog.get_theme_data()
            
            # Générer un ID unique pour le thème
            import uuid
            theme_id = str(uuid.uuid4())
            
            # Ajouter le thème
            self.theme_manager.add_custom_theme(theme_id, theme_data)
            
            # Mettre à jour la liste des thèmes
            self.update_theme_combo()
    
    def on_edit_theme(self) -> None:
        """Gère la modification d'un thème."""
        theme_id = self.theme_combo.currentData()
        if not theme_id:
            return
        
        # Vérifier si c'est un thème prédéfini
        if theme_id in self.theme_manager.THEMES:
            QMessageBox.warning(
                self, "Avertissement",
                "Les thèmes prédéfinis ne peuvent pas être modifiés. Créez un nouveau thème basé sur celui-ci."
            )
            return
        
        # Récupérer les données du thème
        theme_data = self.theme_manager.get_theme(theme_id)
        
        # Ouvrir la boîte de dialogue d'édition de thème
        dialog = ThemeEditorDialog(theme_id, theme_data, self)
        if dialog.exec_():
            theme_data = dialog.get_theme_data()
            
            # Mettre à jour le thème
            self.theme_manager.update_custom_theme(theme_id, theme_data)
            
            # Mettre à jour la liste des thèmes
            self.update_theme_combo()
            
            # Appliquer le thème si c'est le thème actuel
            if theme_id == self.theme_manager.current_theme:
                app = QApplication.instance()
                if app:
                    apply_theme(app)
    
    def on_delete_theme(self) -> None:
        """Gère la suppression d'un thème."""
        theme_id = self.theme_combo.currentData()
        if not theme_id:
            return
        
        # Vérifier si c'est un thème prédéfini
        if theme_id in self.theme_manager.THEMES:
            QMessageBox.warning(
                self, "Avertissement",
                "Les thèmes prédéfinis ne peuvent pas être supprimés."
            )
            return
        
        reply = QMessageBox.question(
            self, "Confirmation",
            "Voulez-vous vraiment supprimer ce thème ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # Supprimer le thème
            self.theme_manager.delete_custom_theme(theme_id)
            
            # Mettre à jour la liste des thèmes
            self.update_theme_combo()
            
            # Appliquer le thème actuel
            app = QApplication.instance()
            if app:
                apply_theme(app)
    
    # Méthodes des tableaux de bord supprimées - fonctionnalité non nécessaire
    
    def on_clear_cache(self) -> None:
        """Gère le vidage du cache."""
        reply = QMessageBox.question(
            self, "Confirmation",
            "Voulez-vous vraiment vider le cache ? Cela peut ralentir temporairement l'application.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            clear_cache()
            self.update_cache_stats()
            
            QMessageBox.information(
                self, "Information",
                "Le cache a été vidé."
            )
    
    def on_clean_cache(self) -> None:
        """Gère le nettoyage du cache expiré."""
        cleaned = clean_expired_cache()
        self.update_cache_stats()
        
        QMessageBox.information(
            self, "Information",
            f"{cleaned} entrées expirées ont été supprimées du cache."
        )
    
    def on_help(self) -> None:
        """Affiche l'aide des paramètres."""
        help_text = """
        <h3>🔧 Aide - Paramètres SOTRAMINE</h3>

        <h4>🎨 Apparence</h4>
        <ul>
        <li><b>Thèmes :</b> Personnalisez l'apparence de l'application</li>
        <li><b>Interface responsive :</b> Adaptation automatique à la taille d'écran</li>
        <li><b>Animations :</b> Effets visuels et transitions</li>
        <li><b>Mode sombre :</b> Activation automatique selon l'heure</li>
        </ul>

        <h4>⚡ Performance</h4>
        <ul>
        <li><b>Cache :</b> Améliore les performances en stockant les données</li>
        <li><b>Durée de vie :</b> Temps de conservation des données en cache</li>
        <li><b>Auto-refresh :</b> Actualisation automatique des données</li>
        </ul>

        <h4>💻 Système</h4>
        <ul>
        <li><b>Informations :</b> Détails sur votre système et l'application</li>
        </ul>

        <h4>🔧 Maintenance</h4>
        <ul>
        <li><b>Réinitialisation :</b> Supprime toutes les données (irréversible)</li>
        </ul>

        <p><i>Pour plus d'aide, contactez l'administrateur système.</i></p>
        """

        msg = QMessageBox(self)
        msg.setWindowTitle("Aide - Paramètres")
        msg.setText(help_text)
        msg.setTextFormat(Qt.RichText)
        msg.setIcon(QMessageBox.Information)
        msg.exec_()

    def on_apply(self) -> None:
        """Gère l'application des paramètres avec feedback amélioré."""
        try:
            # Thème
            theme_id = self.theme_combo.currentData()
            if theme_id:
                self.theme_manager.set_theme(theme_id)

                # Appliquer le thème à l'application
                app = QApplication.instance()
                if app:
                    apply_theme(app)

            # Options d'interface (nouvelles)
            settings_applied = []

            if self.responsive_checkbox.isChecked():
                settings_applied.append("Interface responsive activée")

            if self.animations_checkbox.isChecked():
                settings_applied.append("Animations activées")

            if self.tooltips_checkbox.isChecked():
                settings_applied.append("Infobulles activées")

            if self.dark_mode_checkbox.isChecked():
                settings_applied.append("Mode sombre automatique activé")

            if self.notifications_checkbox.isChecked():
                settings_applied.append("Notifications système activées")

            # Options de performance
            if self.cache_enabled_checkbox.isChecked():
                settings_applied.append("Cache de données activé")

            if self.auto_refresh_checkbox.isChecked():
                settings_applied.append("Actualisation automatique activée")

            cache_ttl = self.cache_ttl_spinbox.value()
            settings_applied.append(f"Durée de vie du cache : {cache_ttl}s")

            # Message de confirmation détaillé
            success_msg = "✅ <b>Paramètres appliqués avec succès !</b><br><br>"
            success_msg += "<b>Paramètres modifiés :</b><ul>"
            for setting in settings_applied:
                success_msg += f"<li>{setting}</li>"
            success_msg += "</ul>"

            msg = QMessageBox(self)
            msg.setWindowTitle("Paramètres Appliqués")
            msg.setText(success_msg)
            msg.setTextFormat(Qt.RichText)
            msg.setIcon(QMessageBox.Information)
            msg.exec_()

        except Exception as e:
            QMessageBox.critical(
                self, "Erreur",
                f"Erreur lors de l'application des paramètres :\n{str(e)}"
            )
    
    def on_reset(self) -> None:
        """Gère la réinitialisation des paramètres avec confirmation renforcée."""
        # Message de confirmation avec détails
        msg = QMessageBox(self)
        msg.setWindowTitle("Réinitialisation des Paramètres")
        msg.setText("🔄 <b>Réinitialiser tous les paramètres ?</b>")
        msg.setInformativeText(
            "Cette action va restaurer tous les paramètres à leurs valeurs par défaut :\n\n"
            "• Thème : Clair\n"
            "• Interface responsive : Activée\n"
            "• Animations : Activées\n"
            "• Infobulles : Activées\n"
            "• Mode sombre : Désactivé\n"
            "• Notifications : Activées\n"
            "• Cache : Activé (300s)\n"
            "• Auto-refresh : Activé"
        )
        msg.setTextFormat(Qt.RichText)
        msg.setIcon(QMessageBox.Question)
        msg.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        msg.setDefaultButton(QMessageBox.No)

        if msg.exec_() == QMessageBox.Yes:
            try:
                # Réinitialiser le thème
                self.theme_manager.set_theme("light")

                # Appliquer le thème à l'application
                app = QApplication.instance()
                if app:
                    apply_theme(app)

                # Mettre à jour la liste des thèmes
                self.update_theme_combo()

                # Réinitialiser les options d'interface
                self.responsive_checkbox.setChecked(True)
                self.animations_checkbox.setChecked(True)
                self.tooltips_checkbox.setChecked(True)
                self.dark_mode_checkbox.setChecked(False)
                self.notifications_checkbox.setChecked(True)

                # Réinitialiser les options de performance
                self.cache_enabled_checkbox.setChecked(True)
                self.cache_ttl_spinbox.setValue(300)
                self.auto_refresh_checkbox.setChecked(True)

                # Message de succès
                success_msg = QMessageBox(self)
                success_msg.setWindowTitle("Réinitialisation Terminée")
                success_msg.setText("✅ <b>Paramètres réinitialisés avec succès !</b>")
                success_msg.setInformativeText("Tous les paramètres ont été restaurés à leurs valeurs par défaut.")
                success_msg.setTextFormat(Qt.RichText)
                success_msg.setIcon(QMessageBox.Information)
                success_msg.exec_()

            except Exception as e:
                QMessageBox.critical(
                    self, "Erreur",
                    f"Erreur lors de la réinitialisation :\n{str(e)}"
                )
    
    def refresh_tab(self) -> None:
        """Rafraîchit l'onglet avec les nouvelles données."""
        self.update_theme_combo()
        self.update_cache_stats()
        
    def on_reset_database(self) -> None:
        """Gère la réinitialisation de la base de données."""
        # Demander le mot de passe
        password, ok = QInputDialog.getText(
            self, 
            "Authentification requise", 
            "Entrez le mot de passe pour réinitialiser la base de données:",
            QLineEdit.Password
        )
        
        if not ok:
            return
        
        # Vérifier le mot de passe
        if password != "sotramine":
            QMessageBox.critical(
                self, 
                "Erreur d'authentification", 
                "Mot de passe incorrect. Opération annulée."
            )
            return
        
        # Demander confirmation
        reply = QMessageBox.warning(
            self, 
            "Confirmation de réinitialisation", 
            "ATTENTION: Vous êtes sur le point de réinitialiser la base de données.\n\n"
            "Toutes les données seront définitivement perdues.\n\n"
            "Êtes-vous absolument sûr de vouloir continuer?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply != QMessageBox.Yes:
            return
        
        try:
            # Fermer la connexion à la base de données
            if hasattr(self.db_manager, 'engine'):
                self.db_manager.engine.dispose()
            
            # Supprimer le fichier de base de données
            # Extraire le nom de la base de données à partir de l'URL de connexion
            if hasattr(self.db_manager, 'engine'):
                db_url = str(self.db_manager.engine.url)
                if "sqlite:///" in db_url:
                    db_path = db_url.split("sqlite:///")[1]
                    logging.info(f"Suppression de la base de données: {db_path}")
                    if os.path.exists(db_path):
                        os.remove(db_path)
                        logging.info(f"Base de données supprimée: {db_path}")
                    else:
                        logging.warning(f"Fichier de base de données non trouvé: {db_path}")
                
            # Vérifier aussi les noms possibles de la base de données
            for possible_db in ["phosphate.db", "production.db"]:
                if os.path.exists(possible_db):
                    logging.info(f"Suppression de la base de données alternative: {possible_db}")
                    os.remove(possible_db)
                    logging.info(f"Base de données alternative supprimée: {possible_db}")
                
            # Réinitialiser la base de données
            if self.db_manager:
                self.db_manager.initialize_database()
                
            QMessageBox.information(
                self, 
                "Réinitialisation terminée", 
                "La base de données a été réinitialisée avec succès.\n\n"
                "L'application va maintenant redémarrer pour appliquer les changements."
            )
            
            # Redémarrer l'application
            QApplication.instance().quit()
            
        except Exception as e:
            logging.error(f"Erreur lors de la réinitialisation de la base de données: {str(e)}", exc_info=True)
            QMessageBox.critical(
                self, 
                "Erreur", 
                f"Une erreur est survenue lors de la réinitialisation de la base de données:\n{str(e)}"
            )