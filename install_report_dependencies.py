"""
Script d'installation des dépendances pour l'export de rapports.
Installe ReportLab (PDF) et python-docx (Word).
"""

import subprocess
import sys
import importlib

def check_package(package_name):
    """Vérifie si un package est installé."""
    try:
        importlib.import_module(package_name)
        return True
    except ImportError:
        return False

def install_package(package_name):
    """Installe un package via pip."""
    try:
        print(f"Installation de {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} installé avec succès")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur lors de l'installation de {package_name}: {e}")
        return False

def main():
    """Installe les dépendances nécessaires."""
    print("🔧 Installation des dépendances pour l'export de rapports SOTRAMINE")
    print("=" * 60)
    
    dependencies = [
        ("reportlab", "ReportLab (export PDF)"),
        ("docx", "python-docx (export Word)")
    ]
    
    installed_count = 0
    
    for package_import, package_description in dependencies:
        print(f"\n📦 Vérification de {package_description}...")
        
        if check_package(package_import):
            print(f"✅ {package_description} déjà installé")
            installed_count += 1
        else:
            print(f"⚠️ {package_description} non trouvé")
            
            # Nom du package pour pip (différent parfois du nom d'import)
            pip_package = "python-docx" if package_import == "docx" else package_import
            
            if install_package(pip_package):
                installed_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Résumé: {installed_count}/{len(dependencies)} dépendances installées")
    
    if installed_count == len(dependencies):
        print("🎉 Toutes les dépendances sont installées !")
        print("✅ L'export PDF et Word est maintenant disponible")
    else:
        print("⚠️ Certaines dépendances n'ont pas pu être installées")
        print("💡 Vous pouvez les installer manuellement avec:")
        print("   pip install reportlab python-docx")
    
    print("\n🚀 Vous pouvez maintenant utiliser l'export de rapports dans SOTRAMINE")

if __name__ == "__main__":
    main()
