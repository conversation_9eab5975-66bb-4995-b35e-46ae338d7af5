#!/usr/bin/env python3
"""
Script de test pour démontrer la logique corrigée de production :
- Production totale = Production de la laverie (produit final)
- Efficacité globale = Production finale / Matière première consommée
- Taux de transformation = Production laverie / Production concassage
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

from datetime import datetime, date
from database.database_manager import DatabaseManager
from models.enums import ProcessStepEnum
from utils.date_utils import normalize_date, format_date_for_display

def test_production_logic():
    """Test de la logique de production corrigée."""
    print("🏭 Test de la logique de production phosphate")
    print("=" * 50)
    
    # Initialiser la base de données
    db_manager = DatabaseManager()
    db_manager.initialize_database()
    
    # Date d'aujourd'hui
    today = normalize_date(date.today())
    date_str = format_date_for_display(today)
    
    print(f"\n📅 Simulation de production pour {date_str}")
    print("\n🔄 Flux de production :")
    print("  1. Réception de matière première")
    print("  2. Concassage (étape intermédiaire)")
    print("  3. Laverie (produit final enrichi)")
    
    # Étape 1 : Concassage
    print(f"\n🔨 Étape 1 - CONCASSAGE")
    matiere_premiere_consommee = 1000.0  # Tonnes de matière première
    production_concassage = 800.0        # Tonnes après concassage (perte de 20%)
    qualite_concassage = 28.5            # % P2O5
    
    print(f"  📥 Matière première consommée: {matiere_premiere_consommee} T")
    print(f"  📤 Production concassage: {production_concassage} T")
    print(f"  🎯 Qualité sortie: {qualite_concassage}% P2O5")
    print(f"  📊 Rendement concassage: {(production_concassage/matiere_premiere_consommee)*100:.1f}%")
    
    # Enregistrer la production de concassage
    db_manager.add_production(
        step=ProcessStepEnum.CONCASSAGE,
        quantity=production_concassage,
        quantity_used=matiere_premiere_consommee,
        quality=f"{qualite_concassage}%",
        production_hours=8.0,
        production_date=today
    )
    
    # Étape 2 : Laverie
    print(f"\n💧 Étape 2 - LAVERIE (Enrichissement)")
    matiere_concassee_consommee = 600.0  # Tonnes de concassé consommé
    production_laverie = 500.0           # Tonnes de produit final (perte de 16.7%)
    qualite_finale = 31.5               # % P2O5 après enrichissement
    
    print(f"  📥 Concassé consommé: {matiere_concassee_consommee} T")
    print(f"  📤 Production finale: {production_laverie} T")
    print(f"  🎯 Qualité finale: {qualite_finale}% P2O5")
    print(f"  📊 Rendement laverie: {(production_laverie/matiere_concassee_consommee)*100:.1f}%")
    
    # Enregistrer la production de laverie
    db_manager.add_production(
        step=ProcessStepEnum.LAVERIE,
        quantity=production_laverie,
        quantity_used=matiere_concassee_consommee,
        quality=f"{qualite_finale}%",
        production_hours=8.0,
        production_date=today
    )
    
    # Calculs des KPI
    print(f"\n📊 CALCUL DES KPI")
    print("-" * 30)
    
    # 1. Production totale = Production de la laverie
    production_totale = production_laverie
    print(f"🎯 Production Totale (Finale): {production_totale} T")
    print(f"   └─ = Production de la laverie (produit final enrichi)")
    
    # 2. Efficacité globale = Production finale / Matière première
    efficacite_globale = (production_totale / matiere_premiere_consommee) * 100
    print(f"⚡ Efficacité Globale: {efficacite_globale:.1f}%")
    print(f"   └─ = {production_totale} T (finale) / {matiere_premiere_consommee} T (matière première)")
    
    # 3. Taux de transformation = Production laverie / Production concassage
    taux_transformation = (production_laverie / production_concassage) * 100
    print(f"🔄 Taux de Transformation: {taux_transformation:.1f}%")
    print(f"   └─ = {production_laverie} T (laverie) / {production_concassage} T (concassage)")
    
    # 4. Qualité finale
    print(f"💎 Qualité Finale: {qualite_finale}% P2O5")
    print(f"   └─ = Qualité du produit enrichi (laverie)")
    
    # Vérification avec la base de données
    print(f"\n🔍 VÉRIFICATION AVEC LA BASE DE DONNÉES")
    print("-" * 40)
    
    # Récupérer les productions
    start_datetime, end_datetime = today, today.replace(hour=23, minute=59, second=59)
    
    crushing_productions = db_manager.get_production_summary(
        step=ProcessStepEnum.CONCASSAGE,
        start_date=start_datetime,
        end_date=end_datetime
    )
    
    washing_productions = db_manager.get_production_summary(
        step=ProcessStepEnum.LAVERIE,
        start_date=start_datetime,
        end_date=end_datetime
    )
    
    # Calculs depuis la DB
    crushing_total = sum(p['quantity'] for p in crushing_productions)
    washing_total = sum(p['quantity'] for p in washing_productions)
    crushing_consumed = sum(p.get('quantity_used', 0) or 0 for p in crushing_productions)
    
    print(f"✅ Production Concassage (DB): {crushing_total} T")
    print(f"✅ Production Laverie (DB): {washing_total} T")
    print(f"✅ Matière première consommée (DB): {crushing_consumed} T")
    
    # KPI calculés
    db_production_totale = washing_total
    db_efficacite_globale = (db_production_totale / crushing_consumed) * 100 if crushing_consumed > 0 else 0
    db_taux_transformation = (washing_total / crushing_total) * 100 if crushing_total > 0 else 0
    
    print(f"\n📈 KPI CALCULÉS DEPUIS LA DB:")
    print(f"🎯 Production Totale: {db_production_totale} T")
    print(f"⚡ Efficacité Globale: {db_efficacite_globale:.1f}%")
    print(f"🔄 Taux de Transformation: {db_taux_transformation:.1f}%")
    
    # Validation
    print(f"\n✅ VALIDATION:")
    print(f"Production totale correcte: {production_totale == db_production_totale}")
    print(f"Efficacité globale correcte: {abs(efficacite_globale - db_efficacite_globale) < 0.1}")
    print(f"Taux transformation correct: {abs(taux_transformation - db_taux_transformation) < 0.1}")
    
    print(f"\n🎉 Test terminé avec succès!")
    print(f"\n💡 Points clés:")
    print(f"  • Production totale = Production de la laverie (produit final)")
    print(f"  • Efficacité globale = Rendement global du processus")
    print(f"  • Taux transformation = Efficacité concassage → laverie")
    print(f"  • Qualité finale = Qualité du produit enrichi")

if __name__ == "__main__":
    test_production_logic()
