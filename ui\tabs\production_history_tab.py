"""
Onglet dédié à l'historique de production avec analyses et filtres avancés.
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, 
                            QGridLayout, QWidget, QComboBox, QDateEdit,
                            QPushButton, QTabWidget, QSplitter, QCheckBox)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import QFont
from datetime import datetime, timedelta, date

from .base_optimized import BaseOptimizedTab
from ui.widgets.professional_widgets import ProfessionalTable, KPILine
from models.enums import ProcessStepEnum, ResourceType
from utils.date_utils import normalize_date, get_date_range_for_day, format_date_for_display

class ProductionHistoryTab(BaseOptimizedTab):
    """Onglet dédié à l'historique et l'analyse de production."""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "📊 Historique Production", parent)
        self.setup_interface()
        self.setup_timers()
        
    def setup_interface(self):
        """Configure l'interface de l'historique."""
        # Layout principal avec splitter vertical
        main_splitter = QSplitter(Qt.Vertical)
        self.main_layout.addWidget(main_splitter)
        
        # === SECTION FILTRES ET KPI ===
        top_widget = QWidget()
        top_widget.setMaximumHeight(200)
        top_layout = QVBoxLayout(top_widget)
        top_layout.setSpacing(10)
        
        self.setup_filters_section(top_layout)
        self.setup_summary_kpi(top_layout)
        
        # === SECTION TABLEAUX ===
        bottom_widget = QWidget()
        bottom_layout = QVBoxLayout(bottom_widget)
        bottom_layout.setSpacing(10)
        
        self.setup_history_tables(bottom_layout)
        
        # Ajouter au splitter
        main_splitter.addWidget(top_widget)
        main_splitter.addWidget(bottom_widget)
        main_splitter.setSizes([200, 600])
        
        # Charger les données initiales
        self.refresh_all_data()
        
    def setup_filters_section(self, layout):
        """Configure la section des filtres."""
        filters_group = QGroupBox("🔍 Filtres et Période")
        filters_group.setFont(QFont("Segoe UI", 9, QFont.Bold))
        filters_group.setMaximumHeight(100)
        filters_layout = QHBoxLayout(filters_group)
        
        # Période
        filters_layout.addWidget(QLabel("Du:"))
        self.start_date = QDateEdit()
        self.start_date.setCalendarPopup(True)
        self.start_date.setDate(QDate.currentDate().addDays(-7))
        self.start_date.setDisplayFormat("dd/MM/yyyy")
        self.start_date.dateChanged.connect(self.refresh_all_data)
        filters_layout.addWidget(self.start_date)
        
        filters_layout.addWidget(QLabel("Au:"))
        self.end_date = QDateEdit()
        self.end_date.setCalendarPopup(True)
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setDisplayFormat("dd/MM/yyyy")
        self.end_date.dateChanged.connect(self.refresh_all_data)
        filters_layout.addWidget(self.end_date)
        
        # Filtres par unité
        filters_layout.addWidget(QLabel("Unité:"))
        self.unit_filter = QComboBox()
        self.unit_filter.addItems(["Toutes", "Concassage", "Laverie"])
        self.unit_filter.currentTextChanged.connect(self.refresh_all_data)
        filters_layout.addWidget(self.unit_filter)
        
        # Boutons de période rapide
        filters_layout.addWidget(QLabel("|"))
        
        week_btn = QPushButton("7 jours")
        week_btn.clicked.connect(lambda: self.set_period_days(7))
        filters_layout.addWidget(week_btn)
        
        month_btn = QPushButton("30 jours")
        month_btn.clicked.connect(lambda: self.set_period_days(30))
        filters_layout.addWidget(month_btn)
        
        filters_layout.addStretch()
        
        # Bouton actualiser
        refresh_btn = QPushButton("🔄 Actualiser")
        refresh_btn.clicked.connect(self.refresh_all_data)
        filters_layout.addWidget(refresh_btn)
        
        layout.addWidget(filters_group)
        
    def setup_summary_kpi(self, layout):
        """Configure les KPI de résumé de la période."""
        kpi_group = QGroupBox("📈 Résumé de la Période")
        kpi_group.setFont(QFont("Segoe UI", 9, QFont.Bold))
        kpi_group.setMaximumHeight(80)
        kpi_layout = QHBoxLayout(kpi_group)
        
        # KPI période
        self.period_total_production = KPILine("Production Totale", "0", "T")
        self.period_avg_quality = KPILine("Qualité Moyenne", "0", "%")
        self.period_total_hours = KPILine("Heures Totales", "0", "h")
        self.period_avg_efficiency = KPILine("Efficacité Moyenne", "0", "%")
        
        kpi_layout.addWidget(self.period_total_production)
        kpi_layout.addWidget(self.period_avg_quality)
        kpi_layout.addWidget(self.period_total_hours)
        kpi_layout.addWidget(self.period_avg_efficiency)
        
        layout.addWidget(kpi_group)
        
    def setup_history_tables(self, layout):
        """Configure les tableaux d'historique."""
        # Onglets pour les différents historiques
        self.history_tabs = QTabWidget()
        
        # Onglet Production
        self.setup_production_history_tab()
        self.history_tabs.addTab(self.production_history_widget, "📈 Productions")
        
        # Onglet Consommations
        self.setup_consumption_history_tab()
        self.history_tabs.addTab(self.consumption_history_widget, "⚡ Consommations")
        
        # Onglet Arrêts
        self.setup_downtime_history_tab()
        self.history_tabs.addTab(self.downtime_history_widget, "⏹️ Arrêts")
        
        # Onglet Analyse
        self.setup_analysis_tab()
        self.history_tabs.addTab(self.analysis_widget, "📊 Analyses")
        
        layout.addWidget(self.history_tabs)
        
    def setup_production_history_tab(self):
        """Configure l'onglet historique de production."""
        self.production_history_widget = QWidget()
        layout = QVBoxLayout(self.production_history_widget)
        
        # En-tête avec statistiques
        stats_layout = QHBoxLayout()
        self.production_count_label = QLabel("Nombre d'enregistrements: 0")
        self.production_count_label.setStyleSheet("font-weight: bold; color: #2E86AB;")
        stats_layout.addWidget(self.production_count_label)
        stats_layout.addStretch()
        
        # Bouton export
        export_btn = QPushButton("📄 Exporter Excel")
        export_btn.clicked.connect(self.export_production_history)
        stats_layout.addWidget(export_btn)
        
        layout.addLayout(stats_layout)
        
        # Tableau de production
        headers = ["Date", "Unité", "Produit (T)", "Consommé (T)", 
                  "Heures", "Qualité (%)", "Rendement (T/h)", "Efficacité (%)"]
        self.production_history_table = ProfessionalTable()
        self.production_history_table.setColumnCount(len(headers))
        self.production_history_table.setHorizontalHeaderLabels(headers)
        self.production_history_table.setSortingEnabled(True)
        layout.addWidget(self.production_history_table)
        
    def setup_consumption_history_tab(self):
        """Configure l'onglet historique des consommations."""
        self.consumption_history_widget = QWidget()
        layout = QVBoxLayout(self.consumption_history_widget)
        
        # En-tête avec filtres spécifiques
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("Ressource:"))
        
        self.resource_filter = QComboBox()
        self.resource_filter.addItems(["Toutes", "Eau", "Énergie", "Réactifs"])
        self.resource_filter.currentTextChanged.connect(self.refresh_consumption_history)
        filter_layout.addWidget(self.resource_filter)
        filter_layout.addStretch()
        
        self.consumption_count_label = QLabel("Nombre d'enregistrements: 0")
        self.consumption_count_label.setStyleSheet("font-weight: bold; color: #2E86AB;")
        filter_layout.addWidget(self.consumption_count_label)
        
        layout.addLayout(filter_layout)
        
        # Tableau des consommations
        headers = ["Date", "Unité", "Ressource", "Quantité", "Unité"]
        self.consumption_history_table = ProfessionalTable()
        self.consumption_history_table.setColumnCount(len(headers))
        self.consumption_history_table.setHorizontalHeaderLabels(headers)
        self.consumption_history_table.setSortingEnabled(True)
        layout.addWidget(self.consumption_history_table)
        
    def setup_downtime_history_tab(self):
        """Configure l'onglet historique des arrêts."""
        self.downtime_history_widget = QWidget()
        layout = QVBoxLayout(self.downtime_history_widget)
        
        # En-tête avec statistiques d'arrêts
        stats_layout = QHBoxLayout()
        self.downtime_count_label = QLabel("Nombre d'arrêts: 0")
        self.downtime_total_label = QLabel("Durée totale: 0 min")
        stats_layout.addWidget(self.downtime_count_label)
        stats_layout.addWidget(self.downtime_total_label)
        stats_layout.addStretch()
        
        layout.addLayout(stats_layout)
        
        # Tableau des arrêts
        headers = ["Date", "Unité", "Raison", "Durée (min)", "Description"]
        self.downtime_history_table = ProfessionalTable()
        self.downtime_history_table.setColumnCount(len(headers))
        self.downtime_history_table.setHorizontalHeaderLabels(headers)
        self.downtime_history_table.setSortingEnabled(True)
        layout.addWidget(self.downtime_history_table)
        
    def setup_analysis_tab(self):
        """Configure l'onglet d'analyse."""
        self.analysis_widget = QWidget()
        layout = QVBoxLayout(self.analysis_widget)
        
        # Analyses par jour/semaine/mois
        analysis_group = QGroupBox("📊 Analyses Temporelles")
        analysis_layout = QVBoxLayout(analysis_group)
        
        # Sélecteur de groupement
        group_layout = QHBoxLayout()
        group_layout.addWidget(QLabel("Grouper par:"))
        
        self.group_by_combo = QComboBox()
        self.group_by_combo.addItems(["Jour", "Semaine", "Mois"])
        self.group_by_combo.currentTextChanged.connect(self.refresh_analysis)
        group_layout.addWidget(self.group_by_combo)
        group_layout.addStretch()
        
        analysis_layout.addLayout(group_layout)
        
        # Tableau d'analyse
        headers = ["Période", "Production (T)", "Qualité Moy (%)", "Heures", "Efficacité (%)"]
        self.analysis_table = ProfessionalTable()
        self.analysis_table.setColumnCount(len(headers))
        self.analysis_table.setHorizontalHeaderLabels(headers)
        analysis_layout.addWidget(self.analysis_table)
        
        layout.addWidget(analysis_group)
        
    def setup_timers(self):
        """Configure les timers pour les mises à jour automatiques."""
        # Timer pour actualisation automatique (toutes les 2 minutes)
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_all_data)
        self.refresh_timer.start(120000)  # 2 minutes

    # === MÉTHODES UTILITAIRES ===

    def set_period_days(self, days):
        """Définit une période de X jours jusqu'à aujourd'hui."""
        end_date = QDate.currentDate()
        start_date = end_date.addDays(-days)
        self.start_date.setDate(start_date)
        self.end_date.setDate(end_date)

    def get_date_range(self):
        """Retourne la plage de dates sélectionnée."""
        start = self.start_date.date().toPyDate()
        end = self.end_date.date().toPyDate()
        start_datetime = normalize_date(start)
        end_datetime = datetime.combine(end, datetime.max.time())
        return start_datetime, end_datetime

    # === MÉTHODES DE RAFRAÎCHISSEMENT ===

    def refresh_all_data(self):
        """Actualise toutes les données."""
        self.refresh_summary_kpi()
        self.refresh_production_history()
        self.refresh_consumption_history()
        self.refresh_downtime_history()
        self.refresh_analysis()

    def refresh_summary_kpi(self):
        """Met à jour les KPI de résumé."""
        try:
            start_datetime, end_datetime = self.get_date_range()

            # Récupérer les productions selon le filtre d'unité
            unit_filter = self.unit_filter.currentText()

            all_productions = []
            if unit_filter in ["Toutes", "Concassage"]:
                crushing_productions = self.db_manager.get_production_summary(
                    step=ProcessStepEnum.CONCASSAGE,
                    start_date=start_datetime,
                    end_date=end_datetime
                )
                all_productions.extend(crushing_productions)

            if unit_filter in ["Toutes", "Laverie"]:
                washing_productions = self.db_manager.get_production_summary(
                    step=ProcessStepEnum.LAVERIE,
                    start_date=start_datetime,
                    end_date=end_datetime
                )
                all_productions.extend(washing_productions)

            # Calculer les KPI
            if all_productions:
                # Production totale (pour l'historique, on peut sommer les deux unités)
                total_production = sum(p['quantity'] for p in all_productions)
                self.period_total_production.update_value(f"{total_production:.1f}")

                # Heures totales
                total_hours = sum(p.get('production_hours', 0) or 0 for p in all_productions)
                self.period_total_hours.update_value(f"{total_hours:.1f}")

                # Qualité moyenne pondérée
                total_weighted_quality = 0
                total_quantity_with_quality = 0

                for p in all_productions:
                    if p.get('quality') and p['quantity'] > 0:
                        try:
                            if isinstance(p['quality'], str) and '%' in p['quality']:
                                quality_val = float(p['quality'].replace('%', '').strip())
                            else:
                                quality_val = float(p['quality'])
                            total_weighted_quality += quality_val * p['quantity']
                            total_quantity_with_quality += p['quantity']
                        except (ValueError, TypeError):
                            pass

                if total_quantity_with_quality > 0:
                    avg_quality = total_weighted_quality / total_quantity_with_quality
                    self.period_avg_quality.update_value(f"{avg_quality:.1f}")
                else:
                    self.period_avg_quality.update_value("0")

                # Efficacité moyenne
                total_consumed = sum(p.get('quantity_used', 0) or 0 for p in all_productions)
                if total_consumed > 0:
                    avg_efficiency = (total_production / total_consumed) * 100
                    self.period_avg_efficiency.update_value(f"{avg_efficiency:.1f}")
                else:
                    self.period_avg_efficiency.update_value("0")
            else:
                # Pas de données
                self.period_total_production.update_value("0")
                self.period_avg_quality.update_value("0")
                self.period_total_hours.update_value("0")
                self.period_avg_efficiency.update_value("0")

        except Exception as e:
            print(f"Erreur mise à jour KPI résumé: {str(e)}")

    def refresh_production_history(self):
        """Actualise l'historique de production."""
        try:
            start_datetime, end_datetime = self.get_date_range()
            unit_filter = self.unit_filter.currentText()

            # Récupérer les productions
            all_productions = []
            if unit_filter in ["Toutes", "Concassage"]:
                crushing_productions = self.db_manager.get_production_summary(
                    step=ProcessStepEnum.CONCASSAGE,
                    start_date=start_datetime,
                    end_date=end_datetime
                )
                for p in crushing_productions:
                    p['unit'] = 'Concassage'
                    all_productions.append(p)

            if unit_filter in ["Toutes", "Laverie"]:
                washing_productions = self.db_manager.get_production_summary(
                    step=ProcessStepEnum.LAVERIE,
                    start_date=start_datetime,
                    end_date=end_datetime
                )
                for p in washing_productions:
                    p['unit'] = 'Laverie'
                    all_productions.append(p)

            # Trier par date (plus récent en premier)
            all_productions.sort(key=lambda x: x['production_date'], reverse=True)

            # Mettre à jour le compteur
            self.production_count_label.setText(f"Nombre d'enregistrements: {len(all_productions)}")

            # Remplir le tableau
            self.populate_production_history_table(all_productions)

        except Exception as e:
            print(f"Erreur actualisation historique production: {str(e)}")

    def populate_production_history_table(self, productions):
        """Remplit le tableau d'historique de production."""
        self.production_history_table.setRowCount(len(productions))

        for row, production in enumerate(productions):
            # Date
            date_str = format_date_for_display(production['production_date'])
            self.production_history_table.setItem(row, 0,
                self.production_history_table.itemClass()(date_str))

            # Unité
            self.production_history_table.setItem(row, 1,
                self.production_history_table.itemClass()(production['unit']))

            # Quantité produite
            self.production_history_table.setItem(row, 2,
                self.production_history_table.itemClass()(f"{production['quantity']:.2f}"))

            # Quantité consommée
            consumed = production.get('quantity_used', 0)
            self.production_history_table.setItem(row, 3,
                self.production_history_table.itemClass()(f"{consumed:.2f}" if consumed else "-"))

            # Heures
            hours = production.get('production_hours', 0)
            self.production_history_table.setItem(row, 4,
                self.production_history_table.itemClass()(f"{hours:.2f}" if hours else "-"))

            # Qualité
            quality = production.get('quality', '')
            if quality:
                if isinstance(quality, (int, float)):
                    quality_display = f"{quality:.1f}%"
                elif isinstance(quality, str) and '%' not in quality:
                    try:
                        quality_float = float(quality)
                        quality_display = f"{quality_float:.1f}%"
                    except (ValueError, TypeError):
                        quality_display = quality
                else:
                    quality_display = quality
            else:
                quality_display = "-"
            self.production_history_table.setItem(row, 5,
                self.production_history_table.itemClass()(quality_display))

            # Rendement
            if hours and hours > 0:
                rendement = production['quantity'] / hours
                self.production_history_table.setItem(row, 6,
                    self.production_history_table.itemClass()(f"{rendement:.2f}"))
            else:
                self.production_history_table.setItem(row, 6,
                    self.production_history_table.itemClass()("-"))

            # Efficacité
            if consumed and consumed > 0:
                efficiency = (production['quantity'] / consumed) * 100
                self.production_history_table.setItem(row, 7,
                    self.production_history_table.itemClass()(f"{efficiency:.1f}%"))
            else:
                self.production_history_table.setItem(row, 7,
                    self.production_history_table.itemClass()("-"))

    def refresh_consumption_history(self):
        """Actualise l'historique des consommations."""
        try:
            start_datetime, end_datetime = self.get_date_range()

            # Récupérer les consommations
            consumptions = self.db_manager.get_resource_consumptions(
                start_date=start_datetime,
                end_date=end_datetime
            )

            # Filtrer par ressource si nécessaire
            resource_filter = self.resource_filter.currentText()
            if resource_filter != "Toutes":
                resource_map = {
                    "Eau": ResourceType.WATER.value,
                    "Énergie": ResourceType.ENERGY.value,
                    "Réactifs": ResourceType.REACTIVE.value
                }
                if resource_filter in resource_map:
                    consumptions = [c for c in consumptions
                                  if c['resource_type'] == resource_map[resource_filter]]

            # Trier par date
            consumptions.sort(key=lambda x: x['consumption_date'], reverse=True)

            # Mettre à jour le compteur
            self.consumption_count_label.setText(f"Nombre d'enregistrements: {len(consumptions)}")

            # Remplir le tableau
            self.populate_consumption_history_table(consumptions)

        except Exception as e:
            print(f"Erreur actualisation historique consommations: {str(e)}")

    def populate_consumption_history_table(self, consumptions):
        """Remplit le tableau d'historique des consommations."""
        self.consumption_history_table.setRowCount(len(consumptions))

        # Mapping des unités
        unit_map = {
            ProcessStepEnum.CONCASSAGE.value: "Concassage",
            ProcessStepEnum.LAVERIE.value: "Laverie"
        }

        unit_display_map = {
            ResourceType.WATER.value: "m³",
            ResourceType.ENERGY.value: "kWh",
            ResourceType.REACTIVE.value: "kg"
        }

        for row, consumption in enumerate(consumptions):
            # Date
            date_str = format_date_for_display(consumption['consumption_date'])
            self.consumption_history_table.setItem(row, 0,
                self.consumption_history_table.itemClass()(date_str))

            # Unité
            unit_name = "Global"
            if consumption.get('step_name'):
                unit_name = unit_map.get(consumption['step_name'], consumption['step_name'])
            self.consumption_history_table.setItem(row, 1,
                self.consumption_history_table.itemClass()(unit_name))

            # Ressource
            self.consumption_history_table.setItem(row, 2,
                self.consumption_history_table.itemClass()(consumption['resource_type']))

            # Quantité
            self.consumption_history_table.setItem(row, 3,
                self.consumption_history_table.itemClass()(f"{consumption['quantity']:.2f}"))

            # Unité de mesure
            unit_display = unit_display_map.get(consumption['resource_type'], "")
            self.consumption_history_table.setItem(row, 4,
                self.consumption_history_table.itemClass()(unit_display))

    def refresh_downtime_history(self):
        """Actualise l'historique des arrêts."""
        try:
            start_datetime, end_datetime = self.get_date_range()
            unit_filter = self.unit_filter.currentText()

            # Récupérer les arrêts
            all_downtimes = []
            if unit_filter in ["Toutes", "Concassage"]:
                crushing_downtimes = self.db_manager.get_downtime_summary(
                    step=ProcessStepEnum.CONCASSAGE,
                    start_date=start_datetime,
                    end_date=end_datetime
                )
                for d in crushing_downtimes:
                    d['unit'] = 'Concassage'
                    all_downtimes.append(d)

            if unit_filter in ["Toutes", "Laverie"]:
                washing_downtimes = self.db_manager.get_downtime_summary(
                    step=ProcessStepEnum.LAVERIE,
                    start_date=start_datetime,
                    end_date=end_datetime
                )
                for d in washing_downtimes:
                    d['unit'] = 'Laverie'
                    all_downtimes.append(d)

            # Trier par date
            all_downtimes.sort(key=lambda x: x['downtime_date'], reverse=True)

            # Calculer les statistiques
            total_duration = sum(d['duration'] for d in all_downtimes)
            self.downtime_count_label.setText(f"Nombre d'arrêts: {len(all_downtimes)}")
            self.downtime_total_label.setText(f"Durée totale: {total_duration:.0f} min")

            # Remplir le tableau
            self.populate_downtime_history_table(all_downtimes)

        except Exception as e:
            print(f"Erreur actualisation historique arrêts: {str(e)}")

    def populate_downtime_history_table(self, downtimes):
        """Remplit le tableau d'historique des arrêts."""
        self.downtime_history_table.setRowCount(len(downtimes))

        for row, downtime in enumerate(downtimes):
            # Date
            date_str = format_date_for_display(downtime['downtime_date'])
            self.downtime_history_table.setItem(row, 0,
                self.downtime_history_table.itemClass()(date_str))

            # Unité
            self.downtime_history_table.setItem(row, 1,
                self.downtime_history_table.itemClass()(downtime['unit']))

            # Raison
            self.downtime_history_table.setItem(row, 2,
                self.downtime_history_table.itemClass()(downtime['reason']))

            # Durée
            self.downtime_history_table.setItem(row, 3,
                self.downtime_history_table.itemClass()(f"{downtime['duration']:.0f}"))

            # Description
            description = downtime.get('description', '') or ''
            self.downtime_history_table.setItem(row, 4,
                self.downtime_history_table.itemClass()(description))

    def refresh_analysis(self):
        """Actualise l'analyse temporelle."""
        try:
            start_datetime, end_datetime = self.get_date_range()
            group_by = self.group_by_combo.currentText()

            # Récupérer toutes les productions
            all_productions = []
            crushing_productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.CONCASSAGE,
                start_date=start_datetime,
                end_date=end_datetime
            )
            washing_productions = self.db_manager.get_production_summary(
                step=ProcessStepEnum.LAVERIE,
                start_date=start_datetime,
                end_date=end_datetime
            )
            all_productions.extend(crushing_productions)
            all_productions.extend(washing_productions)

            # Grouper les données
            grouped_data = self.group_productions_by_period(all_productions, group_by)

            # Remplir le tableau d'analyse
            self.populate_analysis_table(grouped_data)

        except Exception as e:
            print(f"Erreur actualisation analyse: {str(e)}")

    def group_productions_by_period(self, productions, group_by):
        """Groupe les productions par période."""
        grouped = {}

        for production in productions:
            prod_date = production['production_date']

            if group_by == "Jour":
                key = prod_date.strftime("%d/%m/%Y")
            elif group_by == "Semaine":
                # Début de semaine (lundi)
                start_of_week = prod_date - timedelta(days=prod_date.weekday())
                key = f"Semaine du {start_of_week.strftime('%d/%m/%Y')}"
            else:  # Mois
                key = prod_date.strftime("%m/%Y")

            if key not in grouped:
                grouped[key] = {
                    'productions': [],
                    'total_quantity': 0,
                    'total_hours': 0,
                    'total_consumed': 0
                }

            grouped[key]['productions'].append(production)
            grouped[key]['total_quantity'] += production['quantity']
            grouped[key]['total_hours'] += production.get('production_hours', 0) or 0
            grouped[key]['total_consumed'] += production.get('quantity_used', 0) or 0

        return grouped

    def populate_analysis_table(self, grouped_data):
        """Remplit le tableau d'analyse."""
        self.analysis_table.setRowCount(len(grouped_data))

        row = 0
        for period, data in sorted(grouped_data.items()):
            # Période
            self.analysis_table.setItem(row, 0,
                self.analysis_table.itemClass()(period))

            # Production totale
            self.analysis_table.setItem(row, 1,
                self.analysis_table.itemClass()(f"{data['total_quantity']:.1f}"))

            # Qualité moyenne
            total_weighted_quality = 0
            total_quantity_with_quality = 0

            for p in data['productions']:
                if p.get('quality') and p['quantity'] > 0:
                    try:
                        if isinstance(p['quality'], str) and '%' in p['quality']:
                            quality_val = float(p['quality'].replace('%', '').strip())
                        else:
                            quality_val = float(p['quality'])
                        total_weighted_quality += quality_val * p['quantity']
                        total_quantity_with_quality += p['quantity']
                    except (ValueError, TypeError):
                        pass

            if total_quantity_with_quality > 0:
                avg_quality = total_weighted_quality / total_quantity_with_quality
                self.analysis_table.setItem(row, 2,
                    self.analysis_table.itemClass()(f"{avg_quality:.1f}"))
            else:
                self.analysis_table.setItem(row, 2,
                    self.analysis_table.itemClass()("-"))

            # Heures totales
            self.analysis_table.setItem(row, 3,
                self.analysis_table.itemClass()(f"{data['total_hours']:.1f}"))

            # Efficacité moyenne
            if data['total_consumed'] > 0:
                efficiency = (data['total_quantity'] / data['total_consumed']) * 100
                self.analysis_table.setItem(row, 4,
                    self.analysis_table.itemClass()(f"{efficiency:.1f}"))
            else:
                self.analysis_table.setItem(row, 4,
                    self.analysis_table.itemClass()("-"))

            row += 1

    # === MÉTHODES D'EXPORT ===

    def export_production_history(self):
        """Exporte l'historique de production vers Excel."""
        try:
            from PyQt5.QtWidgets import QFileDialog, QMessageBox
            import pandas as pd

            # Demander le fichier de destination
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Exporter Historique Production",
                f"historique_production_{datetime.now().strftime('%Y%m%d')}.xlsx",
                "Fichiers Excel (*.xlsx)"
            )

            if file_path:
                # Récupérer les données
                start_datetime, end_datetime = self.get_date_range()

                # Créer le DataFrame
                data = []
                for row in range(self.production_history_table.rowCount()):
                    row_data = []
                    for col in range(self.production_history_table.columnCount()):
                        item = self.production_history_table.item(row, col)
                        row_data.append(item.text() if item else "")
                    data.append(row_data)

                headers = [self.production_history_table.horizontalHeaderItem(i).text()
                          for i in range(self.production_history_table.columnCount())]

                df = pd.DataFrame(data, columns=headers)
                df.to_excel(file_path, index=False)

                QMessageBox.information(self, "Export Réussi",
                                      f"Historique exporté vers:\n{file_path}")

        except ImportError:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "Erreur",
                              "pandas non installé. Impossible d'exporter vers Excel.")
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Erreur Export", f"Erreur lors de l'export: {str(e)}")

    def refresh_tab(self):
        """Méthode appelée lors du changement d'onglet."""
        self.refresh_all_data()
