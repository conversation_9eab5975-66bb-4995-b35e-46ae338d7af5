{"dashboard_config": {"theme": {"name": "SOTRAMINE Modern", "version": "1.0", "colors": {"primary": "#2E86AB", "secondary": "#3B82F6", "success": "#10B981", "warning": "#F59E0B", "danger": "#EF4444", "purple": "#8B5CF6", "gray_50": "#F9FAFB", "gray_100": "#F3F4F6", "gray_200": "#E5E7EB", "gray_900": "#111827"}}, "layout": {"card_spacing": 20, "section_spacing": 24, "border_radius": 12, "padding": 16}, "main_metrics": {"production_total": {"title": "Production Totale Jour", "unit": "T", "target": 500, "color": "#2E86AB", "icon": "📊"}, "efficiency": {"title": "Efficacité Globale", "unit": "%", "target": 85, "color": "#10B981", "icon": "⚡"}, "quality": {"title": "Qualité Moyenne", "unit": "% P2O5", "target": 30, "color": "#8B5CF6", "icon": "💎"}, "downtime": {"title": "Temps d'Arrêt Total", "unit": "min", "target": 120, "color": "#EF4444", "icon": "⏹️"}}, "production_units": {"crushing": {"name": "Concassage", "description": "Préparation matière première", "color": "#F59E0B", "icon": "🔨", "metrics": [{"name": "production", "label": "Production", "unit": "T"}, {"name": "hours", "label": "Heures actives", "unit": "h"}, {"name": "yield", "label": "Rendement", "unit": "T/h"}]}, "washing": {"name": "<PERSON><PERSON><PERSON>", "description": "Enrichissement phosphate", "color": "#3B82F6", "icon": "💧", "metrics": [{"name": "production", "label": "Production", "unit": "T"}, {"name": "hours", "label": "Heures actives", "unit": "h"}, {"name": "yield", "label": "Rendement", "unit": "T/h"}]}}, "activity_types": [{"type": "production_crushing", "label": "Production concassage", "color": "#F59E0B", "icon": "🔨"}, {"type": "production_washing", "label": "Production laverie", "color": "#3B82F6", "icon": "💧"}, {"type": "maintenance", "label": "Maintenance", "color": "#EF4444", "icon": "🔧"}, {"type": "consumption_water", "label": "Consommation eau", "color": "#06B6D4", "icon": "💧"}, {"type": "consumption_energy", "label": "Consommation énergie", "color": "#F59E0B", "icon": "⚡"}, {"type": "quality_check", "label": "Contrôle qualité", "color": "#8B5CF6", "icon": "🔬"}], "trends": {"period_days": 7, "metrics": [{"key": "production_avg", "label": "Production moyenne", "unit": "T/jour", "format": "decimal_1"}, {"key": "efficiency_avg", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON> moy<PERSON>", "unit": "%", "format": "decimal_1"}, {"key": "downtime_avg", "label": "<PERSON><PERSON> d'arrêt moyen", "unit": "min/jour", "format": "integer"}, {"key": "quality_avg", "label": "Qualité moyenne", "unit": "% P2O5", "format": "decimal_1"}]}, "refresh_intervals": {"dashboard": 30000, "activity": 15000, "trends": 60000}, "targets": {"daily_production": 500, "efficiency_min": 85, "quality_min": 30, "downtime_max": 120}, "alerts": {"production_low": {"threshold": 400, "message": "Production journalière en dessous de l'objectif", "color": "#EF4444"}, "efficiency_low": {"threshold": 80, "message": "Efficacité en dessous du seuil acceptable", "color": "#F59E0B"}, "quality_low": {"threshold": 28, "message": "Qualité en dessous du standard", "color": "#EF4444"}, "downtime_high": {"threshold": 150, "message": "Temps d'arrê<PERSON> excessif", "color": "#EF4444"}}, "export": {"formats": ["excel", "pdf", "csv"], "default_filename": "dashboard_sotramine_{date}", "include_charts": true, "include_trends": true}, "widgets": {"card": {"min_width": 200, "min_height": 120, "shadow": "0 4px 12px rgba(0,0,0,0.1)", "hover_effect": true}, "chart": {"height": 300, "colors": ["#2E86AB", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6"], "grid": true, "legend": true}, "table": {"row_height": 40, "header_height": 45, "stripe_rows": true, "sortable": true}}, "responsive": {"breakpoints": {"mobile": 768, "tablet": 1024, "desktop": 1200}, "grid_columns": {"mobile": 1, "tablet": 2, "desktop": 4}}}}