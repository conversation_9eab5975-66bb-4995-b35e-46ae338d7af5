#!/usr/bin/env python3
"""
Script de test pour démontrer les nouvelles fonctionnalités de saisie par jour
avec possibilité de remplir les jours précédents.
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

from datetime import datetime, timedelta, date
from database.database_manager import DatabaseManager
from models.enums import ProcessStepEnum, ResourceType
from utils.date_utils import normalize_date, format_date_for_display

def test_date_functionality():
    """Test des fonctionnalités de gestion des dates."""
    print("🧪 Test des fonctionnalités de gestion des dates")
    print("=" * 50)
    
    # Initialiser la base de données
    db_manager = DatabaseManager()
    db_manager.initialize_database()
    
    # Test 1: Ajouter des productions pour différents jours
    print("\n📅 Test 1: Ajout de productions pour différents jours")
    
    # Aujourd'hui
    today = normalize_date(date.today())
    print(f"Ajout production pour aujourd'hui: {format_date_for_display(today)}")
    db_manager.add_production(
        step=ProcessStepEnum.CONCASSAGE,
        quantity=150.0,
        quality="30.5%",
        production_hours=8.0,
        production_date=today
    )
    
    # Hier
    yesterday = normalize_date(date.today() - timedelta(days=1))
    print(f"Ajout production pour hier: {format_date_for_display(yesterday)}")
    db_manager.add_production(
        step=ProcessStepEnum.CONCASSAGE,
        quantity=120.0,
        quality="29.8%",
        production_hours=7.5,
        production_date=yesterday
    )
    
    # Il y a 2 jours
    two_days_ago = normalize_date(date.today() - timedelta(days=2))
    print(f"Ajout production pour il y a 2 jours: {format_date_for_display(two_days_ago)}")
    db_manager.add_production(
        step=ProcessStepEnum.LAVERIE,
        quantity=100.0,
        quality="31.2%",
        production_hours=8.0,
        production_date=two_days_ago
    )
    
    # Test 2: Ajouter des consommations pour différents jours
    print("\n⚡ Test 2: Ajout de consommations pour différents jours")
    
    # Consommations d'aujourd'hui
    db_manager.add_resource_consumption(
        resource_type=ResourceType.ENERGY,
        quantity=500.0,
        step=ProcessStepEnum.CONCASSAGE,
        consumption_date=today
    )
    
    # Consommations d'hier
    db_manager.add_resource_consumption(
        resource_type=ResourceType.WATER,
        quantity=250.0,
        step=ProcessStepEnum.LAVERIE,
        consumption_date=yesterday
    )
    
    # Test 3: Ajouter des arrêts pour différents jours
    print("\n⏹️ Test 3: Ajout d'arrêts pour différents jours")
    
    # Arrêt aujourd'hui
    db_manager.add_downtime(
        step=ProcessStepEnum.CONCASSAGE,
        reason="Maintenance préventive",
        duration=45,
        description="Changement des filtres",
        downtime_date=today
    )
    
    # Arrêt hier
    db_manager.add_downtime(
        step=ProcessStepEnum.LAVERIE,
        reason="Panne équipement",
        duration=120,
        description="Problème pompe principale",
        downtime_date=yesterday
    )
    
    # Test 4: Vérification des données par jour
    print("\n📊 Test 4: Vérification des données par jour")
    
    for i in range(3):
        test_date = normalize_date(date.today() - timedelta(days=i))
        date_str = format_date_for_display(test_date)
        
        print(f"\n--- Données pour {date_str} ---")
        
        # Productions
        productions = db_manager.get_production_summary(
            step=None,  # Toutes les étapes
            start_date=test_date,
            end_date=test_date.replace(hour=23, minute=59, second=59)
        )
        
        if productions:
            for prod in productions:
                step_name = prod.get('step_name', 'Inconnu')
                quantity = prod.get('quantity', 0)
                quality = prod.get('quality', 'N/A')
                hours = prod.get('production_hours', 0)
                print(f"  📈 {step_name}: {quantity}T, Qualité: {quality}, Heures: {hours}h")
        else:
            print("  📈 Aucune production")
        
        # Consommations
        consumptions = db_manager.get_resource_consumptions(
            start_date=test_date,
            end_date=test_date.replace(hour=23, minute=59, second=59)
        )
        
        if consumptions:
            for cons in consumptions:
                resource = cons.get('resource_type', 'Inconnu')
                quantity = cons.get('quantity', 0)
                step_name = cons.get('step_name', 'Global')
                print(f"  ⚡ {resource}: {quantity} ({step_name})")
        else:
            print("  ⚡ Aucune consommation")
        
        # Arrêts
        downtimes_crushing = db_manager.get_downtime_summary(
            step=ProcessStepEnum.CONCASSAGE,
            start_date=test_date,
            end_date=test_date.replace(hour=23, minute=59, second=59)
        )
        
        downtimes_washing = db_manager.get_downtime_summary(
            step=ProcessStepEnum.LAVERIE,
            start_date=test_date,
            end_date=test_date.replace(hour=23, minute=59, second=59)
        )
        
        all_downtimes = downtimes_crushing + downtimes_washing
        
        if all_downtimes:
            for dt in all_downtimes:
                reason = dt.get('reason', 'Inconnu')
                duration = dt.get('duration', 0)
                print(f"  ⏹️ Arrêt: {reason} ({duration} min)")
        else:
            print("  ⏹️ Aucun arrêt")
    
    print("\n✅ Test terminé avec succès!")
    print("\n🎯 Fonctionnalités testées:")
    print("  - Saisie de production par jour (sans heure)")
    print("  - Saisie de consommations par jour")
    print("  - Saisie d'arrêts par jour")
    print("  - Possibilité de remplir les jours précédents")
    print("  - Normalisation des dates (toujours 00:00:00)")
    print("  - Récupération des données par jour spécifique")

if __name__ == "__main__":
    test_date_functionality()
