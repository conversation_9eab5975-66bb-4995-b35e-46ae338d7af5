"""
Utilitaires d'export de rapports vers PDF et Word.
Convertit les rapports markdown en formats professionnels.
"""

import os
import logging
from datetime import date
from typing import Optional
import tempfile
import subprocess
import platform

try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib import colors
    from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    logging.warning("ReportLab non disponible. Installation: pip install reportlab")

try:
    from docx import Document
    from docx.shared import Inches, Pt
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.enum.table import WD_TABLE_ALIGNMENT
    PYTHON_DOCX_AVAILABLE = True
except ImportError:
    PYTHON_DOCX_AVAILABLE = False
    logging.warning("python-docx non disponible. Installation: pip install python-docx")

class RapportExporter:
    """Exporteur de rapports vers différents formats."""
    
    def __init__(self):
        self.temp_dir = tempfile.gettempdir()
        
    def export_to_pdf(self, markdown_content: str, output_path: str, 
                     title: str = "Rapport de Production") -> bool:
        """
        Exporte un rapport markdown vers PDF.
        
        Args:
            markdown_content: Contenu markdown du rapport
            output_path: Chemin de sortie du PDF
            title: Titre du document
            
        Returns:
            bool: True si succès, False sinon
        """
        if not REPORTLAB_AVAILABLE:
            logging.error("ReportLab non disponible pour l'export PDF")
            return False
            
        try:
            # Créer le document PDF
            doc = SimpleDocTemplate(output_path, pagesize=A4,
                                  rightMargin=72, leftMargin=72,
                                  topMargin=72, bottomMargin=18)
            
            # Styles
            styles = getSampleStyleSheet()
            
            # Style personnalisé pour le titre
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=TA_CENTER,
                textColor=colors.HexColor('#2E86AB')
            )
            
            # Style pour les en-têtes de section
            section_style = ParagraphStyle(
                'SectionHeader',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12,
                spaceBefore=20,
                textColor=colors.HexColor('#1F2937')
            )
            
            # Style pour le texte normal
            normal_style = ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontSize=10,
                spaceAfter=6,
                leading=12
            )
            
            # Construire le contenu
            story = []
            
            # Titre principal
            story.append(Paragraph(title, title_style))
            story.append(Spacer(1, 20))
            
            # Parser le markdown et convertir
            lines = markdown_content.split('\n')
            current_table_data = []
            in_table = False
            
            for line in lines:
                line = line.strip()
                
                if not line:
                    if not in_table:
                        story.append(Spacer(1, 6))
                    continue
                
                # Titre principal (# )
                if line.startswith('# ') and not line.startswith('## '):
                    continue  # Déjà ajouté comme titre
                
                # Titre de section (## )
                elif line.startswith('## '):
                    if in_table and current_table_data:
                        self._add_table_to_story(story, current_table_data)
                        current_table_data = []
                        in_table = False
                    
                    section_title = line[3:].strip()
                    # Nettoyer les emojis pour PDF
                    section_title = self._clean_emojis(section_title)
                    story.append(Paragraph(section_title, section_style))
                
                # Ligne de tableau
                elif '|' in line and not line.startswith('---'):
                    if not in_table:
                        in_table = True
                        current_table_data = []
                    
                    # Parser la ligne de tableau
                    cells = [cell.strip() for cell in line.split('|')[1:-1]]
                    if cells:  # Ignorer les lignes vides
                        current_table_data.append(cells)
                
                # Ligne de séparation markdown
                elif line.startswith('---'):
                    if in_table and current_table_data:
                        self._add_table_to_story(story, current_table_data)
                        current_table_data = []
                        in_table = False
                    story.append(Spacer(1, 12))
                
                # Texte normal
                else:
                    if in_table and current_table_data:
                        self._add_table_to_story(story, current_table_data)
                        current_table_data = []
                        in_table = False
                    
                    if line and not line.startswith('**') and not line.startswith('*'):
                        # Nettoyer le texte
                        clean_line = self._clean_markdown_text(line)
                        if clean_line:
                            story.append(Paragraph(clean_line, normal_style))
            
            # Ajouter la dernière table si nécessaire
            if in_table and current_table_data:
                self._add_table_to_story(story, current_table_data)
            
            # Générer le PDF
            doc.build(story)
            
            logging.info(f"Rapport PDF généré: {output_path}")
            return True
            
        except Exception as e:
            logging.error(f"Erreur génération PDF: {str(e)}")
            return False
    
    def export_to_word(self, markdown_content: str, output_path: str,
                      title: str = "Rapport de Production") -> bool:
        """
        Exporte un rapport markdown vers Word.
        
        Args:
            markdown_content: Contenu markdown du rapport
            output_path: Chemin de sortie du document Word
            title: Titre du document
            
        Returns:
            bool: True si succès, False sinon
        """
        if not PYTHON_DOCX_AVAILABLE:
            logging.error("python-docx non disponible pour l'export Word")
            return False
            
        try:
            # Créer le document Word
            doc = Document()
            
            # Titre principal
            title_paragraph = doc.add_heading(title, 0)
            title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Parser le markdown et convertir
            lines = markdown_content.split('\n')
            current_table_data = []
            in_table = False
            
            for line in lines:
                line = line.strip()
                
                if not line:
                    if not in_table:
                        doc.add_paragraph()
                    continue
                
                # Titre principal (# )
                if line.startswith('# ') and not line.startswith('## '):
                    continue  # Déjà ajouté comme titre
                
                # Titre de section (## )
                elif line.startswith('## '):
                    if in_table and current_table_data:
                        self._add_word_table(doc, current_table_data)
                        current_table_data = []
                        in_table = False
                    
                    section_title = line[3:].strip()
                    section_title = self._clean_emojis(section_title)
                    doc.add_heading(section_title, level=1)
                
                # Ligne de tableau
                elif '|' in line and not line.startswith('---'):
                    if not in_table:
                        in_table = True
                        current_table_data = []
                    
                    # Parser la ligne de tableau
                    cells = [cell.strip() for cell in line.split('|')[1:-1]]
                    if cells:  # Ignorer les lignes vides
                        current_table_data.append(cells)
                
                # Ligne de séparation markdown
                elif line.startswith('---'):
                    if in_table and current_table_data:
                        self._add_word_table(doc, current_table_data)
                        current_table_data = []
                        in_table = False
                    doc.add_paragraph()
                
                # Texte normal
                else:
                    if in_table and current_table_data:
                        self._add_word_table(doc, current_table_data)
                        current_table_data = []
                        in_table = False
                    
                    if line and not line.startswith('**') and not line.startswith('*'):
                        # Nettoyer le texte
                        clean_line = self._clean_markdown_text(line)
                        if clean_line:
                            doc.add_paragraph(clean_line)
            
            # Ajouter la dernière table si nécessaire
            if in_table and current_table_data:
                self._add_word_table(doc, current_table_data)
            
            # Sauvegarder le document
            doc.save(output_path)
            
            logging.info(f"Rapport Word généré: {output_path}")
            return True
            
        except Exception as e:
            logging.error(f"Erreur génération Word: {str(e)}")
            return False
    
    def _add_table_to_story(self, story, table_data):
        """Ajoute un tableau au story PDF."""
        if not table_data:
            return
            
        # Style du tableau
        table_style = TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2E86AB')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ])
        
        # Nettoyer les données du tableau
        clean_data = []
        for row in table_data:
            clean_row = [self._clean_markdown_text(cell) for cell in row]
            clean_data.append(clean_row)
        
        table = Table(clean_data)
        table.setStyle(table_style)
        story.append(table)
        story.append(Spacer(1, 12))
    
    def _add_word_table(self, doc, table_data):
        """Ajoute un tableau au document Word."""
        if not table_data:
            return
            
        # Créer le tableau
        table = doc.add_table(rows=len(table_data), cols=len(table_data[0]))
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.CENTER
        
        # Remplir le tableau
        for i, row_data in enumerate(table_data):
            row = table.rows[i]
            for j, cell_data in enumerate(row_data):
                cell = row.cells[j]
                cell.text = self._clean_markdown_text(cell_data)
                
                # Style de l'en-tête
                if i == 0:
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.bold = True
        
        doc.add_paragraph()
    
    def _clean_emojis(self, text: str) -> str:
        """Supprime les emojis du texte."""
        # Liste des emojis courants à supprimer
        emojis = ['📋', '🏭', '⏹️', '💰', '🔬', '📦', '📊', '🎯', '🔨', '💧', 
                 '⚠️', '✅', '❌', '📈', '📉', '🔧', '📄', '🚀', '👁️', '💾']
        
        for emoji in emojis:
            text = text.replace(emoji, '')
        
        return text.strip()
    
    def _clean_markdown_text(self, text: str) -> str:
        """Nettoie le texte markdown."""
        # Supprimer les emojis
        text = self._clean_emojis(text)
        
        # Supprimer les marqueurs markdown
        text = text.replace('**', '').replace('*', '')
        text = text.replace('`', '').replace('#', '')
        
        # Nettoyer les espaces
        text = ' '.join(text.split())
        
        return text.strip()

def export_rapport(markdown_content: str, format_type: str, date_production: date,
                  output_dir: str = "rapports") -> Optional[str]:
    """
    Exporte un rapport vers le format spécifié.
    
    Args:
        markdown_content: Contenu markdown du rapport
        format_type: Type de format ("PDF (.pdf)" ou "Word (.docx)")
        date_production: Date de production pour le nom de fichier
        output_dir: Dossier de sortie
        
    Returns:
        str: Chemin du fichier généré ou None si erreur
    """
    # Créer le dossier de sortie
    os.makedirs(output_dir, exist_ok=True)
    
    # Déterminer l'extension et le nom de fichier
    if "PDF" in format_type:
        extension = "pdf"
        base_name = f"rapport_production_{date_production.strftime('%Y%m%d')}"
    elif "Word" in format_type:
        extension = "docx"
        base_name = f"rapport_production_{date_production.strftime('%Y%m%d')}"
    else:
        logging.error(f"Format non supporté: {format_type}")
        return None
    
    output_path = os.path.join(output_dir, f"{base_name}.{extension}")
    
    # Exporter selon le format
    exporter = RapportExporter()
    
    if extension == "pdf":
        success = exporter.export_to_pdf(markdown_content, output_path)
    elif extension == "docx":
        success = exporter.export_to_word(markdown_content, output_path)
    else:
        success = False
    
    return output_path if success else None

# Installation des dépendances si nécessaire
def install_dependencies():
    """Installe les dépendances nécessaires pour l'export."""
    try:
        if not REPORTLAB_AVAILABLE:
            print("Installation de ReportLab pour l'export PDF...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "reportlab"])
            
        if not PYTHON_DOCX_AVAILABLE:
            print("Installation de python-docx pour l'export Word...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "python-docx"])
            
        print("Dépendances installées avec succès !")
        return True
        
    except Exception as e:
        print(f"Erreur installation dépendances: {str(e)}")
        return False

if __name__ == "__main__":
    # Test de l'exporteur
    test_markdown = """# Rapport de Production Test

## 📋 Informations Générales

| **Champ** | **Valeur** |
|-----------|------------|
| Date | 27/06/2025 |
| Production | 487.5 T |

## 🏭 Production

Test de contenu avec **texte en gras** et *italique*.

---

Fin du rapport de test.
"""
    
    exporter = RapportExporter()
    
    # Test PDF
    if REPORTLAB_AVAILABLE:
        success_pdf = exporter.export_to_pdf(test_markdown, "test_rapport.pdf")
        print(f"Export PDF: {'✅ Succès' if success_pdf else '❌ Échec'}")
    
    # Test Word
    if PYTHON_DOCX_AVAILABLE:
        success_word = exporter.export_to_word(test_markdown, "test_rapport.docx")
        print(f"Export Word: {'✅ Succès' if success_word else '❌ Échec'}")
