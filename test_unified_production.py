#!/usr/bin/env python3
"""
Script de test pour l'onglet de production unifié.
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

from PyQt5.QtWidgets import QApplication
from database.database_manager import DatabaseManager
from ui.tabs.unified_production_tab import UnifiedProductionTab

def test_unified_production_tab():
    """Test de l'onglet de production unifié."""
    app = QApplication(sys.argv)
    
    # Initialiser la base de données
    db_manager = DatabaseManager()
    db_manager.initialize_database()
    
    # Créer l'onglet unifié
    unified_tab = UnifiedProductionTab(db_manager)
    unified_tab.show()
    
    print("✅ Onglet de production unifié créé avec succès!")
    print("📊 Fonctionnalités disponibles:")
    print("   - KPI globaux de production")
    print("   - Saisie pour Concassage et Laverie")
    print("   - Gestion des consommations")
    print("   - Gestion des arrêts")
    print("   - Tableaux de données unifiés")
    
    # Lancer l'application
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_unified_production_tab()
