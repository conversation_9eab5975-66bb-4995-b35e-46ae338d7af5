# Onglet de Production Unifié - SOTRAMINE PHOSPHATE

## Vue d'ensemble

L'onglet de production unifié regroupe la gestion des deux unités de production principales (Concassage et Laverie) avec leurs consommations et arrêts dans une interface cohérente et centralisée.

## Fonctionnalités

### 🏭 Interface Unifiée
- **Vue d'ensemble globale** : KPI consolidés pour les deux unités
- **Saisie par unité** : Onglets séparés pour Concassage et Laverie
- **Gestion des arrêts** : Interface centralisée pour tous les arrêts
- **Tableaux de données** : Historiques unifiés avec filtres

### 📊 KPI Globaux
- Production totale du jour
- Production par unité (Concassage/Laverie)
- Efficacité globale
- Qualité moyenne pondérée

### 🔨 Unité Concassage
- Saisie de production (quantité, consommation, heures, qualité)
- Consommation d'énergie
- Calcul automatique du rendement et de l'efficacité

### 💧 Unité Laverie
- Saisie de production (quantité lavée, consommée, qualités entrée/sortie, heures)
- Consommations multiples (eau, réactifs, énergie)
- Suivi de la qualité d'enrichissement

### ⏹️ Gestion des Arrêts
- Saisie centralisée pour toutes les unités
- Raisons prédéfinies et personnalisables
- Durée et description détaillée
- Historique complet des arrêts

### 📈 Tableaux de Données
1. **Production** : Historique unifié avec filtres par unité
2. **Consommations** : Toutes les ressources par unité
3. **Arrêts** : Historique complet des temps d'arrêt

## Structure Technique

### Fichiers Créés/Modifiés
- `ui/tabs/unified_production_tab.py` : Nouvel onglet unifié
- `ui/tabs/__init__.py` : Import du nouvel onglet
- `ui/main_window.py` : Intégration dans l'interface principale

### Architecture
```
UnifiedProductionTab
├── Zone Gauche (Contrôles)
│   ├── KPI Globaux
│   ├── Onglets Unités
│   │   ├── Concassage
│   │   └── Laverie
│   └── Gestion Arrêts
└── Zone Droite (Données)
    ├── Tableau Production
    ├── Tableau Consommations
    └── Tableau Arrêts
```

## Avantages

### ✅ Pour l'Utilisateur
- **Vue d'ensemble complète** : Toutes les informations sur un seul écran
- **Navigation simplifiée** : Plus besoin de naviguer entre 3-4 onglets
- **Interface cohérente** : Même design et fonctionnalités pour toutes les unités
- **Efficacité accrue** : Saisie et consultation plus rapides

### ✅ Pour la Maintenance
- **Code centralisé** : Une seule classe pour toute la production
- **Réutilisation** : Composants communs entre les unités
- **Évolutivité** : Facile d'ajouter de nouvelles unités
- **Cohérence** : Même logique métier partout

## Migration

### Anciens Onglets
Les anciens onglets individuels sont conservés mais commentés dans `main_window.py` :
- `OptimizedCrushingTab` (Concassage)
- `OptimizedLaverieTab` (Laverie)
- `ConsumptionTab` (Consommations)
- `HourCounterTab` (Compteurs horaires)

### Données
- **Compatibilité totale** : Utilise les mêmes tables de base de données
- **Pas de migration nécessaire** : Les données existantes sont préservées
- **Fonctionnalités identiques** : Toutes les fonctions sont conservées

## Utilisation

1. **Lancer l'application** : `python main.py`
2. **Accéder à l'onglet** : "Production Unifiée"
3. **Saisir des données** : Utiliser les onglets Concassage/Laverie
4. **Gérer les arrêts** : Section dédiée en bas à gauche
5. **Consulter l'historique** : Onglets de données à droite

## Test

Pour tester uniquement l'onglet unifié :
```bash
python test_unified_production.py
```

## Support

En cas de problème, les anciens onglets peuvent être réactivés en décommentant les lignes correspondantes dans `ui/main_window.py`.
