"""
Onglet Rapports réorganisé avec génération automatique et interface moderne.
Intègre les gabarits de rapports simplifiés et complets.
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                            QLabel, QComboBox, QPushButton, QDateEdit, QScrollArea,
                            QTabWidget, QTextEdit, QMessageBox, QProgressBar, QFrame,
                            QSplitter, QFileDialog, QCheckBox, QSpinBox)
from PyQt5.QtCore import Qt, QDate, QTimer, QThread, pyqtSignal, QDateTime
from PyQt5.QtGui import QFont, QIcon, QPixmap, QPainter
from datetime import datetime, timedelta, date
import logging
import os
import subprocess
import platform

from .base_optimized import BaseOptimizedTab
from ui.widgets.professional_widgets import KPICard, StatusIndicator, ProfessionalTable
from ui.design.modern_dashboard_theme import ModernCard, ModernProductionTheme
from utils.rapport_generator import RapportProductionGenerator
from utils.rapport_simplifie_generator import RapportSimplifieGenerator

class ReportGenerationThread(QThread):
    """Thread pour générer les rapports en arrière-plan."""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    report_generated = pyqtSignal(str, str)  # (report_content, file_path)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, db_manager, report_type, date_production, options=None):
        super().__init__()
        self.db_manager = db_manager
        self.report_type = report_type
        self.date_production = date_production
        self.options = options or {}
        
    def run(self):
        """Génère le rapport selon le type."""
        try:
            self.progress_updated.emit(10)
            self.status_updated.emit("Initialisation du générateur...")
            
            if self.report_type == "simplifie":
                generator = RapportSimplifieGenerator(self.db_manager)
                template_name = "simplifié"
            else:
                generator = RapportProductionGenerator(self.db_manager)
                template_name = "complet"
                
            self.progress_updated.emit(30)
            self.status_updated.emit(f"Collecte des données pour le rapport {template_name}...")
            
            # Générer le rapport
            rapport_content = generator.generer_rapport_journalier(self.date_production)
            
            self.progress_updated.emit(70)
            self.status_updated.emit("Sauvegarde du rapport...")
            
            # Sauvegarder
            file_path = generator.sauvegarder_rapport(rapport_content, self.date_production)
            
            self.progress_updated.emit(100)
            self.status_updated.emit("Rapport généré avec succès !")
            
            self.report_generated.emit(rapport_content, file_path)
            
        except Exception as e:
            logging.error(f"Erreur génération rapport: {str(e)}")
            self.error_occurred.emit(f"Erreur lors de la génération: {str(e)}")

class ReorganizedReportsTab(BaseOptimizedTab):
    """Onglet Rapports réorganisé avec interface moderne."""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "📄 Rapports", parent)
        self.current_report_content = ""
        self.current_file_path = ""
        self.generation_thread = None
        self.setup_interface()
        
    def setup_interface(self):
        """Configure l'interface réorganisée."""
        # Zone de défilement principale
        main_scroll = QScrollArea()
        main_scroll.setWidgetResizable(True)
        main_scroll.setStyleSheet(f"""
            QScrollArea {{
                background-color: {ModernProductionTheme.GRAY_50};
                border: none;
            }}
        """)
        self.main_layout.addWidget(main_scroll)
        
        # Widget contenu principal
        main_content = QWidget()
        main_layout = QVBoxLayout(main_content)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        main_scroll.setWidget(main_content)
        
        # En-tête
        self.setup_header(main_layout)
        
        # Splitter principal
        main_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(main_splitter)
        
        # Panneau de contrôle (gauche)
        self.setup_control_panel(main_splitter)
        
        # Panneau de prévisualisation (droite)
        self.setup_preview_panel(main_splitter)
        
        # Configurer les proportions
        main_splitter.setSizes([400, 600])
        
    def setup_header(self, layout):
        """Configure l'en-tête de l'onglet."""
        header_frame = QFrame()
        header_frame.setStyleSheet(f"""
            QFrame {{
                background: linear-gradient(135deg, {ModernProductionTheme.PRIMARY_BLUE} 0%, {ModernProductionTheme.INDIGO} 100%);
                border-radius: 12px;
                padding: 16px;
            }}
        """)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(16, 16, 16, 16)
        
        # Titre et description
        title_layout = QVBoxLayout()
        
        title = QLabel("📄 Génération de Rapports de Production")
        title.setStyleSheet("""
            color: white;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
        """)
        title_layout.addWidget(title)
        
        subtitle = QLabel("Créez des rapports automatiques complets ou simplifiés")
        subtitle.setStyleSheet("""
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        """)
        title_layout.addWidget(subtitle)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        # Statut système
        status_label = QLabel("🟢 Système Opérationnel")
        status_label.setStyleSheet("""
            color: white;
            font-size: 12px;
            font-weight: 600;
        """)
        header_layout.addWidget(status_label)
        
        layout.addWidget(header_frame)
        
    def setup_control_panel(self, splitter):
        """Configure le panneau de contrôle."""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        control_layout.setSpacing(16)
        control_layout.setContentsMargins(0, 0, 0, 0)
        
        # Section Configuration
        self.setup_configuration_section(control_layout)
        
        # Section Options
        self.setup_options_section(control_layout)
        
        # Section Actions
        self.setup_actions_section(control_layout)
        
        # Section Historique
        self.setup_history_section(control_layout)
        
        control_layout.addStretch()
        splitter.addWidget(control_widget)
        
    def setup_configuration_section(self, layout):
        """Configure la section de configuration."""
        config_group = QGroupBox("⚙️ Configuration du Rapport")
        config_group.setFont(QFont("Segoe UI", 10, QFont.Bold))
        config_group.setStyleSheet(ModernProductionTheme.get_card_style())
        config_layout = QVBoxLayout(config_group)
        config_layout.setSpacing(12)
        
        # Type de rapport
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("Type de rapport:"))
        
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems([
            "📋 Rapport Simplifié (6 sections)",
            "📊 Rapport Complet (10 sections)"
        ])
        self.report_type_combo.setStyleSheet(ModernProductionTheme.get_input_style())
        self.report_type_combo.currentTextChanged.connect(self.on_report_type_changed)
        type_layout.addWidget(self.report_type_combo)
        
        config_layout.addLayout(type_layout)
        
        # Date de production
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel("Date de production:"))
        
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setStyleSheet(ModernProductionTheme.get_input_style())
        date_layout.addWidget(self.date_edit)
        
        config_layout.addLayout(date_layout)
        
        # Boutons de date rapide
        quick_date_layout = QHBoxLayout()
        
        today_btn = QPushButton("Aujourd'hui")
        today_btn.clicked.connect(lambda: self.date_edit.setDate(QDate.currentDate()))
        today_btn.setStyleSheet(ModernProductionTheme.get_button_style(ModernProductionTheme.SUCCESS_GREEN))
        quick_date_layout.addWidget(today_btn)
        
        yesterday_btn = QPushButton("Hier")
        yesterday_btn.clicked.connect(lambda: self.date_edit.setDate(QDate.currentDate().addDays(-1)))
        yesterday_btn.setStyleSheet(ModernProductionTheme.get_button_style(ModernProductionTheme.WARNING_ORANGE))
        quick_date_layout.addWidget(yesterday_btn)
        
        config_layout.addLayout(quick_date_layout)
        
        layout.addWidget(config_group)
        
    def setup_options_section(self, layout):
        """Configure la section des options."""
        options_group = QGroupBox("🔧 Options Avancées")
        options_group.setFont(QFont("Segoe UI", 10, QFont.Bold))
        options_group.setStyleSheet(ModernProductionTheme.get_card_style())
        options_layout = QVBoxLayout(options_group)
        options_layout.setSpacing(8)
        
        # Options d'export
        self.include_charts_cb = QCheckBox("Inclure les graphiques")
        self.include_charts_cb.setChecked(True)
        options_layout.addWidget(self.include_charts_cb)
        
        self.include_details_cb = QCheckBox("Inclure les détails techniques")
        self.include_details_cb.setChecked(True)
        options_layout.addWidget(self.include_details_cb)
        
        self.auto_open_cb = QCheckBox("Ouvrir automatiquement après génération")
        self.auto_open_cb.setChecked(True)
        options_layout.addWidget(self.auto_open_cb)
        
        # Format d'export
        format_layout = QHBoxLayout()
        format_layout.addWidget(QLabel("Format:"))
        
        self.format_combo = QComboBox()
        self.format_combo.addItems(["Markdown (.md)", "PDF (.pdf)", "Word (.docx)"])
        self.format_combo.setStyleSheet(ModernProductionTheme.get_input_style())
        format_layout.addWidget(self.format_combo)
        
        options_layout.addLayout(format_layout)
        
        layout.addWidget(options_group)
        
    def setup_actions_section(self, layout):
        """Configure la section des actions."""
        actions_group = QGroupBox("🚀 Actions")
        actions_group.setFont(QFont("Segoe UI", 10, QFont.Bold))
        actions_group.setStyleSheet(ModernProductionTheme.get_card_style())
        actions_layout = QVBoxLayout(actions_group)
        actions_layout.setSpacing(12)
        
        # Bouton génération principal
        self.generate_btn = QPushButton("📊 Générer le Rapport")
        self.generate_btn.setStyleSheet(ModernProductionTheme.get_button_style(ModernProductionTheme.PRIMARY_BLUE))
        self.generate_btn.clicked.connect(self.generate_report)
        actions_layout.addWidget(self.generate_btn)
        
        # Barre de progression
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid {ModernProductionTheme.GRAY_300};
                border-radius: 6px;
                text-align: center;
                font-weight: 600;
            }}
            QProgressBar::chunk {{
                background-color: {ModernProductionTheme.PRIMARY_BLUE};
                border-radius: 5px;
            }}
        """)
        actions_layout.addWidget(self.progress_bar)
        
        # Label de statut
        self.status_label = QLabel("")
        self.status_label.setStyleSheet(f"color: {ModernProductionTheme.GRAY_600}; font-size: 12px;")
        self.status_label.setVisible(False)
        actions_layout.addWidget(self.status_label)
        
        # Boutons d'action secondaires
        secondary_layout = QHBoxLayout()
        
        self.preview_btn = QPushButton("👁️ Aperçu")
        self.preview_btn.setEnabled(False)
        self.preview_btn.clicked.connect(self.preview_report)
        self.preview_btn.setStyleSheet(ModernProductionTheme.get_button_style(ModernProductionTheme.SUCCESS_GREEN))
        secondary_layout.addWidget(self.preview_btn)
        
        self.export_btn = QPushButton("💾 Exporter")
        self.export_btn.setEnabled(False)
        self.export_btn.clicked.connect(self.export_report)
        self.export_btn.setStyleSheet(ModernProductionTheme.get_button_style(ModernProductionTheme.WARNING_ORANGE))
        secondary_layout.addWidget(self.export_btn)
        
        actions_layout.addLayout(secondary_layout)
        
        layout.addWidget(actions_group)
        
    def setup_history_section(self, layout):
        """Configure la section historique."""
        history_group = QGroupBox("📚 Rapports Récents")
        history_group.setFont(QFont("Segoe UI", 10, QFont.Bold))
        history_group.setStyleSheet(ModernProductionTheme.get_card_style())
        history_layout = QVBoxLayout(history_group)
        
        # Liste des rapports récents
        self.recent_reports_list = QWidget()
        recent_layout = QVBoxLayout(self.recent_reports_list)
        recent_layout.setSpacing(4)
        
        # Charger les rapports récents
        self.load_recent_reports()
        
        # Scroll area pour la liste
        recent_scroll = QScrollArea()
        recent_scroll.setWidget(self.recent_reports_list)
        recent_scroll.setWidgetResizable(True)
        recent_scroll.setMaximumHeight(200)
        recent_scroll.setStyleSheet(f"""
            QScrollArea {{
                border: 1px solid {ModernProductionTheme.GRAY_200};
                border-radius: 6px;
                background-color: white;
            }}
        """)
        
        history_layout.addWidget(recent_scroll)
        
        # Bouton actualiser
        refresh_btn = QPushButton("🔄 Actualiser")
        refresh_btn.clicked.connect(self.load_recent_reports)
        refresh_btn.setStyleSheet(ModernProductionTheme.get_button_style(ModernProductionTheme.GRAY_500))
        history_layout.addWidget(refresh_btn)
        
        layout.addWidget(history_group)
        
    def setup_preview_panel(self, splitter):
        """Configure le panneau de prévisualisation."""
        preview_widget = QWidget()
        preview_layout = QVBoxLayout(preview_widget)
        preview_layout.setSpacing(12)
        preview_layout.setContentsMargins(0, 0, 0, 0)
        
        # En-tête de prévisualisation
        preview_header = QLabel("👁️ Aperçu du Rapport")
        preview_header.setFont(QFont("Segoe UI", 12, QFont.Bold))
        preview_header.setStyleSheet(f"color: {ModernProductionTheme.GRAY_900}; margin-bottom: 8px;")
        preview_layout.addWidget(preview_header)
        
        # Zone de prévisualisation
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        self.preview_text.setPlaceholderText("Générez un rapport pour voir l'aperçu ici...")
        self.preview_text.setStyleSheet(f"""
            QTextEdit {{
                border: 1px solid {ModernProductionTheme.GRAY_200};
                border-radius: 8px;
                background-color: white;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                line-height: 1.4;
                padding: 12px;
            }}
        """)
        preview_layout.addWidget(self.preview_text)
        
        splitter.addWidget(preview_widget)
        
    def on_report_type_changed(self):
        """Appelé quand le type de rapport change."""
        report_type = self.report_type_combo.currentText()
        
        if "Simplifié" in report_type:
            description = "Rapport compact avec 6 sections essentielles (4-6 pages)"
        else:
            description = "Rapport détaillé avec 10 sections complètes (8-12 pages)"
            
        # Mettre à jour la description (si widget existe)
        # self.description_label.setText(description)
        
    def generate_report(self):
        """Génère le rapport sélectionné."""
        if self.generation_thread and self.generation_thread.isRunning():
            QMessageBox.warning(self, "Génération en cours", 
                              "Une génération de rapport est déjà en cours. Veuillez patienter.")
            return
            
        # Déterminer le type de rapport
        report_type = "simplifie" if "Simplifié" in self.report_type_combo.currentText() else "complet"
        
        # Date sélectionnée
        selected_date = self.date_edit.date().toPyDate()
        
        # Options
        options = {
            'include_charts': self.include_charts_cb.isChecked(),
            'include_details': self.include_details_cb.isChecked(),
            'format': self.format_combo.currentText()
        }
        
        # Démarrer la génération
        self.start_generation(report_type, selected_date, options)
        
    def start_generation(self, report_type, date_production, options):
        """Démarre la génération en arrière-plan."""
        # Interface en mode génération
        self.generate_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setVisible(True)
        self.status_label.setText("Initialisation...")
        
        # Créer et démarrer le thread
        self.generation_thread = ReportGenerationThread(
            self.db_manager, report_type, date_production, options
        )
        
        self.generation_thread.progress_updated.connect(self.progress_bar.setValue)
        self.generation_thread.status_updated.connect(self.status_label.setText)
        self.generation_thread.report_generated.connect(self.on_report_generated)
        self.generation_thread.error_occurred.connect(self.on_generation_error)
        
        self.generation_thread.start()
        
    def on_report_generated(self, content, file_path):
        """Appelé quand le rapport est généré."""
        self.current_report_content = content
        self.current_file_path = file_path
        
        # Réinitialiser l'interface
        self.generate_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_label.setVisible(False)
        
        # Activer les boutons
        self.preview_btn.setEnabled(True)
        self.export_btn.setEnabled(True)
        
        # Afficher l'aperçu
        self.preview_text.setPlainText(content[:2000] + "..." if len(content) > 2000 else content)
        
        # Actualiser la liste des rapports récents
        self.load_recent_reports()
        
        # Ouvrir automatiquement si demandé
        if self.auto_open_cb.isChecked():
            self.open_file(file_path)
            
        # Message de succès
        QMessageBox.information(self, "✅ Succès", 
                              f"Rapport généré avec succès !\n\nFichier: {os.path.basename(file_path)}")
        
    def on_generation_error(self, error_message):
        """Appelé en cas d'erreur de génération."""
        self.generate_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_label.setVisible(False)
        
        QMessageBox.critical(self, "❌ Erreur", f"Erreur lors de la génération:\n\n{error_message}")
        
    def preview_report(self):
        """Affiche l'aperçu complet du rapport."""
        if not self.current_report_content:
            return
            
        # Créer une fenêtre de prévisualisation
        preview_dialog = QWidget()
        preview_dialog.setWindowTitle("Aperçu du Rapport")
        preview_dialog.setGeometry(200, 200, 800, 600)
        
        layout = QVBoxLayout(preview_dialog)
        
        text_edit = QTextEdit()
        text_edit.setPlainText(self.current_report_content)
        text_edit.setReadOnly(True)
        layout.addWidget(text_edit)
        
        preview_dialog.show()
        
    def export_report(self):
        """Exporte le rapport dans le format sélectionné."""
        if not self.current_file_path:
            return
            
        # Ouvrir le dossier contenant le fichier
        folder_path = os.path.dirname(self.current_file_path)
        self.open_file(folder_path)
        
    def open_file(self, file_path):
        """Ouvre un fichier avec l'application par défaut."""
        try:
            if platform.system() == "Windows":
                os.startfile(file_path)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", file_path])
            else:  # Linux
                subprocess.run(["xdg-open", file_path])
        except Exception as e:
            logging.error(f"Erreur ouverture fichier: {str(e)}")
            
    def load_recent_reports(self):
        """Charge la liste des rapports récents."""
        # Effacer la liste actuelle
        layout = self.recent_reports_list.layout()
        while layout.count():
            child = layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
                
        # Charger les fichiers du dossier rapports
        reports_folder = "rapports"
        if os.path.exists(reports_folder):
            files = []
            for filename in os.listdir(reports_folder):
                if filename.endswith('.md'):
                    file_path = os.path.join(reports_folder, filename)
                    file_time = os.path.getmtime(file_path)
                    files.append((filename, file_path, file_time))
                    
            # Trier par date de modification (plus récent en premier)
            files.sort(key=lambda x: x[2], reverse=True)
            
            # Afficher les 10 plus récents
            for filename, file_path, file_time in files[:10]:
                self.add_recent_report_item(filename, file_path, file_time)
                
        if layout.count() == 0:
            no_reports_label = QLabel("Aucun rapport trouvé")
            no_reports_label.setStyleSheet(f"color: {ModernProductionTheme.GRAY_500}; font-style: italic;")
            layout.addWidget(no_reports_label)
            
    def add_recent_report_item(self, filename, file_path, file_time):
        """Ajoute un élément à la liste des rapports récents."""
        item_frame = QFrame()
        item_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {ModernProductionTheme.GRAY_50};
                border-radius: 6px;
                padding: 8px;
                margin: 2px;
            }}
            QFrame:hover {{
                background-color: {ModernProductionTheme.GRAY_100};
            }}
        """)
        
        item_layout = QVBoxLayout(item_frame)
        item_layout.setSpacing(4)
        item_layout.setContentsMargins(8, 6, 8, 6)
        
        # Nom du fichier
        name_label = QLabel(filename)
        name_label.setFont(QFont("Segoe UI", 9, QFont.Bold))
        name_label.setStyleSheet(f"color: {ModernProductionTheme.GRAY_900};")
        item_layout.addWidget(name_label)
        
        # Date de modification
        date_str = datetime.fromtimestamp(file_time).strftime("%d/%m/%Y %H:%M")
        date_label = QLabel(f"Modifié: {date_str}")
        date_label.setStyleSheet(f"color: {ModernProductionTheme.GRAY_600}; font-size: 10px;")
        item_layout.addWidget(date_label)
        
        # Bouton d'ouverture
        open_btn = QPushButton("📂 Ouvrir")
        open_btn.clicked.connect(lambda: self.open_file(file_path))
        open_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ModernProductionTheme.PRIMARY_BLUE};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 10px;
            }}
            QPushButton:hover {{
                background-color: {ModernProductionTheme.PRIMARY_BLUE}dd;
            }}
        """)
        item_layout.addWidget(open_btn)
        
        self.recent_reports_list.layout().addWidget(item_frame)
        
    def refresh_tab(self):
        """Méthode appelée lors du changement d'onglet."""
        self.load_recent_reports()
